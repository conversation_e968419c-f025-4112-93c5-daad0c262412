import { useState, useCallback, useRef, useEffect } from 'react';
import { fetchPredictionLines } from '../services/api';
import { predictionLineDataManager } from '../utils/predictionDataManager';
import { useSSEConnection } from './useSSEConnection';
import useUserStore from '../store/useUserStore';

interface PredictionLineDataState {
  data: any[];
  isLoading: boolean;
  isLoadingMore: boolean;
  error: string | null;
}

// 默认加载的预测点数量
const DEFAULT_LIMIT = 480;

/**
 * 预测折线数据获取和管理Hook (SSE版本)
 * 使用SSE实时推送获取预测折线数据，替代轮询方式
 * @returns 预测折线数据状态和操作方法
 */
export const usePredictionLineDataSSE = () => {
  // 状态管理
  const [state, setState] = useState<PredictionLineDataState>({
    data: [],
    isLoading: true,
    isLoadingMore: false,
    error: null
  });

  // 引用存储
  const isRefreshingRef = useRef<boolean>(false);

  // 用户认证状态
  const { isAuthenticated, user } = useUserStore();

  // 使用统一SSE连接获取数据库更新通知
  const {
    connectionStatus,
    error: sseError,
    databaseUpdateTrigger
  } = useSSEConnection();

  // 权限状态缓存
  const permissionStatusRef = useRef<{ hasPermission: boolean; lastUser: string | null }>({
    hasPermission: false,
    lastUser: null
  });

  // 检查用户是否有预测折线访问权限
  const hasPermission = useCallback(() => {
    const currentUserKey = isAuthenticated && user ? `${user.email}-${user.role}` : null;

    // 如果用户状态没有变化，直接返回缓存结果
    if (permissionStatusRef.current.lastUser === currentUserKey) {
      return permissionStatusRef.current.hasPermission;
    }

    let hasAccess = false;

    if (!isAuthenticated || !user) {
      if (permissionStatusRef.current.lastUser !== null) {
        console.log('[预测折线权限] 用户未认证');
      }
    } else if (user.role === 'normal') {
      if (permissionStatusRef.current.lastUser !== currentUserKey) {
        console.log(`[预测折线权限] 用户 ${user.email} 权限不足 (${user.role})`);
      }
    } else {
      hasAccess = true;
      if (permissionStatusRef.current.lastUser !== currentUserKey) {
        console.log(`[预测折线权限] 用户 ${user.email} 有权限访问 (${user.role})`);
      }
    }

    // 更新缓存
    permissionStatusRef.current = {
      hasPermission: hasAccess,
      lastUser: currentUserKey
    };

    return hasAccess;
  }, [isAuthenticated, user]);

  // 初始加载预测折线数据
  const loadData = useCallback(async () => {
    if (isRefreshingRef.current) return;

    // 检查权限
    if (!hasPermission()) {
      console.log('[预测折线] 用户无权限，跳过数据加载');
      setState(prev => ({
        ...prev,
        data: [],
        isLoading: false,
        error: null
      }));
      return;
    }

    isRefreshingRef.current = true;
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      // 使用新的数据管理器初始化加载
      const initialData = await predictionLineDataManager.loadInitialData();
      console.log('预测折线缓存统计:', predictionLineDataManager.getCacheStats());

      setState(prev => ({
        ...prev,
        data: initialData,
        error: null
      }));
    } catch (err) {
      console.error('预测折线数据加载失败:', err);
      setState(prev => ({
        ...prev,
        error: '获取预测折线数据失败'
      }));
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
      isRefreshingRef.current = false;
    }
  }, [hasPermission]);

  // SSE预测折线数据推送已移除，改为仅通过数据库更新通知重新加载数据

  // 加载更多历史预测折线数据
  const loadMoreHistory = useCallback(async (oldestTime?: number) => {
    if (state.isLoadingMore || !hasPermission()) return;

    setState(prev => ({ ...prev, isLoadingMore: true }));

    try {
      // 使用新的数据管理器加载更多历史数据
      // 注意：预测折线数据管理器需要实现loadMoreHistory方法
      console.log(`加载更多预测折线数据，oldestTime: ${oldestTime ? new Date(oldestTime).toISOString() : '未指定'}`);

      // 临时使用原有逻辑，直到预测折线数据管理器完善
      let endTime: number;
      if (oldestTime) {
        endTime = oldestTime;
      } else {
        const currentData = state.data;
        if (currentData.length === 0) {
          setState(prev => ({ ...prev, isLoadingMore: false }));
          return;
        }
        endTime = Math.min(...currentData.map(item => {
          const time = typeof item.time === 'string' ? parseInt(item.time) : Number(item.time);
          return time * 1000;
        }));
      }

      const moreData = await fetchPredictionLines(endTime, DEFAULT_LIMIT);
      if (moreData && Array.isArray(moreData) && moreData.length > 0) {
        console.log(`加载到 ${moreData.length} 条更多预测折线数据`);

        // 简单合并，去重逻辑
        const existingData = state.data;
        const uniqueMap = new Map();

        existingData.forEach(item => {
          const timeKey = typeof item.time === 'string' ? item.time : item.time.toString();
          uniqueMap.set(timeKey, item);
        });

        moreData.forEach(item => {
          const timeKey = typeof item.time === 'string' ? item.time : item.time.toString();
          uniqueMap.set(timeKey, item);
        });

        const mergedData = Array.from(uniqueMap.values()).sort((a, b) => {
          const timeA = typeof a.time === 'string' ? parseInt(a.time) : Number(a.time);
          const timeB = typeof b.time === 'string' ? parseInt(b.time) : Number(b.time);
          return timeA - timeB;
        });

        setState(prev => ({
          ...prev,
          data: mergedData,
          isLoadingMore: false
        }));
      } else {
        console.log('没有更多预测折线数据可加载');
        setState(prev => ({ ...prev, isLoadingMore: false }));
      }
    } catch (err) {
      console.error('加载历史预测折线数据失败:', err);
      setState(prev => ({ ...prev, isLoadingMore: false }));
    }
  }, [state.isLoadingMore, state.data, hasPermission]);

  // SSE预测折线数据监听已移除

  // 首次加载数据
  useEffect(() => {
    loadData();
  }, [loadData]);

  // 监听数据库更新通知，智能合并新数据
  useEffect(() => {
    if (databaseUpdateTrigger > 0 && hasPermission()) {
      console.log('[预测折线] 收到数据库更新通知，使用智能更新');

      predictionLineDataManager.handleDatabaseUpdate().then(updatedData => {
        if (updatedData && updatedData.length > 0) {
          setState(prev => ({
            ...prev,
            data: updatedData,
            error: null
          }));
        }
      }).catch(error => {
        console.error('[预测折线] 数据库更新处理失败:', error);
      });
    }
  }, [databaseUpdateTrigger, hasPermission]);

  // 监听用户状态变化，当用户重新获得权限时重新加载数据
  useEffect(() => {
    if (hasPermission() && state.data.length === 0 && !state.isLoading) {
      console.log('[预测折线] 用户权限状态变化，重新加载数据');
      loadData();
    }
  }, [hasPermission, state.data.length, state.isLoading, loadData]);

  return {
    predictionLineData: state.data,
    isLoading: state.isLoading,
    isLoadingMore: state.isLoadingMore,

    // 分离错误状态
    dataError: state.error,        // 业务数据错误
    connectionError: sseError,     // SSE连接错误
    error: state.error || sseError, // 保持向后兼容

    loadData,
    loadMoreHistory,

    // SSE相关状态
    connectionStatus,
    isConnected: connectionStatus === 'connected',
    hasPermission: hasPermission()
  };
};
