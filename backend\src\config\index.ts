import dotenv from 'dotenv';

// 加载.env文件中的环境变量
dotenv.config();

/**
 * 系统配置对象
 * 包含所有应用程序需要的配置参数
 */
const config = {
  // 服务器端口配置，默认为4000
  port: process.env.PORT || 4000,

  // MongoDB连接URI
  mongoUri: process.env.MONGO_URI || 'mongodb://localhost:27017/btc-prediction',

  // 预测触发时间间隔配置
  predictIntervals: {
    oddHour: ['02', '28', '58'], // 奇数小时触发时间（分钟）
    evenHour: ['28'], // 偶数小时触发时间（分钟）
  },

  // CORS跨域配置
  cors: {
    origin: process.env.CORS_ORIGIN || '*', // 允许的源
  },

  // JWT密钥配置已移至专用的认证中间件
};

export default config;