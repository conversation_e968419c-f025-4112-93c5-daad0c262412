import { createUnifiedDataManager } from './cacheManager';
import { fetchThirtyMinKlineData } from '../services/api';
import { KLineData } from '../types/chartTypes';

/**
 * K线数据统一管理器
 * 继承UnifiedDataManager，实现K线数据的具体加载逻辑
 */
class KLineDataManager {
  private unifiedManager: any;
  
  constructor() {
    this.unifiedManager = createUnifiedDataManager('实时K线');
    
    // 重写冷数据加载器的fetchFromServer方法
    this.setupColdDataLoader();
  }
  
  private setupColdDataLoader() {
    // 获取冷数据加载器实例并重写fetchFromServer方法
    const coldDataLoader = (this.unifiedManager as any).coldDataLoader;
    
    // 重写fetchFromServer方法
    coldDataLoader.fetchFromServer = async (startTime: number, endTime: number): Promise<KLineData[]> => {
      console.log(`[K线冷数据] 从服务器加载: ${new Date(startTime)} - ${new Date(endTime)}`);
      
      try {
        // 使用现有的API，传入endTime作为历史数据的结束时间
        const data = await fetchThirtyMinKlineData(endTime);
        
        if (data && Array.isArray(data)) {
          // 过滤出指定时间范围内的数据
          const filteredData = data.filter(item => {
            const itemTime = typeof item.time === 'string' ? parseInt(item.time) : Number(item.time);
            const itemTimeMs = itemTime * 1000; // 转换为毫秒
            return itemTimeMs >= startTime && itemTimeMs <= endTime;
          });
          
          console.log(`[K线冷数据] 服务器返回 ${data.length} 条，过滤后 ${filteredData.length} 条`);
          return filteredData;
        }
        
        return [];
      } catch (error) {
        console.error(`[K线冷数据] 加载失败:`, error);
        throw error;
      }
    };
    
    // 重写统一管理器的updateHotDataCacheSilently方法
    this.unifiedManager.updateHotDataCacheSilently = async (): Promise<void> => {
      try {
        console.log(`[K线热数据] 静默更新开始`);
        
        // 获取最新的热数据（最近30天）
        const latestHotData = await fetchThirtyMinKlineData();
        
        if (latestHotData && Array.isArray(latestHotData)) {
          // 使用智能清理的增量更新
          const hotCacheManager = (this.unifiedManager as any).hotCacheManager;
          const { cleanedItems } = hotCacheManager.incrementalUpdateHotData(latestHotData);
          
          console.log(`[K线热数据] 静默更新完成，智能清理保护了视口边界数据`);
        }
      } catch (error) {
        console.error(`[K线热数据] 静默更新失败:`, error);
        throw error;
      }
    };
  }
  
  // 处理SSE实时数据更新
  handleSSERealtimeUpdate(sseData: KLineData[]): KLineData[] | null {
    return this.unifiedManager.handleSSERealtimeUpdate(sseData);
  }
  
  // 处理数据库更新通知
  async handleDatabaseUpdate(): Promise<KLineData[] | null> {
    return await this.unifiedManager.handleDatabaseUpdate();
  }
  
  // 视口变化处理
  async handleViewportChange(startTime: number, endTime: number): Promise<KLineData[]> {
    return await this.unifiedManager.handleViewportChange(startTime, endTime);
  }
  
  // 获取立即可用的数据
  getImmediatelyAvailableData(): KLineData[] {
    return this.unifiedManager.getImmediatelyAvailableData();
  }
  
  // 初始化加载（兼容现有逻辑）
  async loadInitialData(): Promise<KLineData[]> {
    try {
      console.log(`[K线数据] 初始化加载开始`);
      
      // 获取历史数据
      const historicalData = await fetchThirtyMinKlineData();
      
      if (historicalData && Array.isArray(historicalData) && historicalData.length > 0) {
        console.log(`[K线数据] 获取到 ${historicalData.length} 条历史数据`);
        
        // 使用智能增量更新
        const hotCacheManager = (this.unifiedManager as any).hotCacheManager;
        const { updatedData } = hotCacheManager.incrementalUpdateHotData(historicalData);
        
        console.log(`[K线数据] 初始化完成，缓存 ${updatedData.length} 条数据`);
        return updatedData;
      }
      
      return [];
    } catch (error) {
      console.error(`[K线数据] 初始化加载失败:`, error);
      throw error;
    }
  }
  
  // 智能合并数据（兼容现有的数据合并逻辑）
  smartMergeData(existingData: KLineData[], newData: KLineData[]): KLineData[] {
    const uniqueMap = new Map();
    
    // 先添加现有数据
    existingData.forEach(item => {
      const timeKey = typeof item.time === 'string' ? item.time : item.time.toString();
      uniqueMap.set(timeKey, {
        ...item,
        priority: (item as any).fromDatabase ? 'database' : 'existing'
      });
    });
    
    // 再添加新数据，SSE数据始终可以更新价格
    newData.forEach(item => {
      const timeKey = typeof item.time === 'string' ? item.time : item.time.toString();
      
      uniqueMap.set(timeKey, {
        ...item,
        priority: (item as any).fromBinance ? 'sse' : 'database',
        fromBinance: (item as any).fromBinance || false
      });
    });
    
    // 转换为数组并排序，移除priority字段
    const mergedData = Array.from(uniqueMap.values()).map(item => {
      const { priority, ...cleanItem } = item;
      return cleanItem;
    }) as KLineData[];
    
    mergedData.sort((a, b) => {
      const timeA = typeof a.time === 'string' ? parseInt(a.time) : Number(a.time);
      const timeB = typeof b.time === 'string' ? parseInt(b.time) : Number(b.time);
      return timeA - timeB;
    });
    
    // 使用智能增量更新
    const hotCacheManager = (this.unifiedManager as any).hotCacheManager;
    const { updatedData } = hotCacheManager.incrementalUpdateHotData(mergedData);
    
    return updatedData;
  }
  
  // 加载更多历史数据（兼容现有逻辑）
  async loadMoreHistory(oldestTime: number): Promise<KLineData[]> {
    try {
      console.log(`[K线数据] 加载更多历史数据，时间戳: ${oldestTime}`);
      
      // 获取指定时间之前的数据
      const olderCandles = await fetchThirtyMinKlineData(oldestTime * 1000);
      
      if (olderCandles && Array.isArray(olderCandles) && olderCandles.length > 0) {
        console.log(`[K线数据] 获取到 ${olderCandles.length} 条更早的数据`);
        
        // 获取当前所有数据
        const hotCacheManager = (this.unifiedManager as any).hotCacheManager;
        const existingData = hotCacheManager.getAllHotData();
        
        // 智能合并数据
        return this.smartMergeData(existingData, olderCandles);
      }
      
      return [];
    } catch (error) {
      console.error(`[K线数据] 加载更多历史数据失败:`, error);
      throw error;
    }
  }
  
  // 获取缓存统计信息
  getCacheStats() {
    return this.unifiedManager.getCacheStats();
  }
  
  // 强制清理所有缓存
  clearAll() {
    this.unifiedManager.clearAll();
  }
}

// 导出单例实例
export const klineDataManager = new KLineDataManager();
