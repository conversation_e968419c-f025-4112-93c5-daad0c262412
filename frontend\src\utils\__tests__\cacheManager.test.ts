/**
 * 缓存管理器测试
 * 测试30天热数据缓存和智能清理功能
 */

import { createUnifiedDataManager } from '../cacheManager';

// 模拟数据生成器
const generateMockData = (startTime: number, count: number, interval: number = 30 * 60 * 1000) => {
  return Array.from({ length: count }, (_, index) => ({
    time: startTime + (index * interval),
    open: 50000 + Math.random() * 1000,
    high: 51000 + Math.random() * 1000,
    low: 49000 + Math.random() * 1000,
    close: 50000 + Math.random() * 1000,
    volume: Math.random() * 1000000
  }));
};

describe('缓存管理器测试', () => {
  let dataManager: any;
  const now = Date.now();
  const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000);
  const sixtyDaysAgo = now - (60 * 24 * 60 * 60 * 1000);

  beforeEach(() => {
    dataManager = createUnifiedDataManager('测试');
    
    // 模拟updateHotDataCacheSilently方法
    dataManager.updateHotDataCacheSilently = async () => {
      console.log('模拟静默更新');
    };
  });

  test('热数据缓存基本功能', () => {
    // 生成最近30天的测试数据
    const hotData = generateMockData(thirtyDaysAgo, 100);
    
    // 测试增量更新
    const hotCacheManager = (dataManager as any).hotCacheManager;
    const result = hotCacheManager.incrementalUpdateHotData(hotData);
    
    expect(result.updatedData).toHaveLength(100);
    expect(result.cleanedItems).toHaveLength(0); // 没有过期数据
    
    // 测试缓存统计
    const stats = hotCacheManager.getCacheStats();
    expect(stats.total).toBe(100);
    expect(stats.hot).toBe(100);
    expect(stats.cold).toBe(0);
  });

  test('智能清理功能', () => {
    const hotCacheManager = (dataManager as any).hotCacheManager;
    
    // 添加一些热数据和过期数据
    const hotData = generateMockData(thirtyDaysAgo, 50);
    const oldData = generateMockData(sixtyDaysAgo, 50);
    const mixedData = [...hotData, ...oldData];
    
    const result = hotCacheManager.incrementalUpdateHotData(mixedData);
    
    // 应该只保留热数据，清理过期数据
    expect(result.updatedData).toHaveLength(50);
    expect(result.cleanedItems).toHaveLength(0); // 过期数据不会被添加到缓存
    
    const stats = hotCacheManager.getCacheStats();
    expect(stats.hot).toBe(50);
    expect(stats.cold).toBe(0);
  });

  test('视口边界保护功能', () => {
    const hotCacheManager = (dataManager as any).hotCacheManager;
    
    // 设置跨越边界的视口
    const viewportStart = sixtyDaysAgo;
    const viewportEnd = now;
    hotCacheManager.updateViewport(viewportStart, viewportEnd);
    
    // 添加一些数据，包括在视口范围内的过期数据
    const hotData = generateMockData(thirtyDaysAgo, 50);
    const boundaryData = generateMockData(viewportStart, 20); // 在视口内但已过期
    
    // 先添加边界数据到缓存
    hotCacheManager.incrementalUpdateHotData(boundaryData);
    
    // 然后添加热数据，这应该触发智能清理但保护视口内数据
    const result = hotCacheManager.incrementalUpdateHotData(hotData);
    
    // 验证视口状态
    const viewport = hotCacheManager.currentViewport;
    expect(viewport.isInHotRange).toBe(true);
    expect(viewport.isInColdRange).toBe(true);
    expect(viewport.isFullyInHotRange).toBe(false);
    expect(viewport.isFullyInColdRange).toBe(false);
  });

  test('SSE实时数据更新', () => {
    // 模拟SSE实时数据
    const sseData = generateMockData(now - 60000, 5); // 最近1分钟的数据
    
    // 设置视口在热数据范围
    const hotCacheManager = (dataManager as any).hotCacheManager;
    hotCacheManager.updateViewport(now - 3600000, now); // 最近1小时
    
    const result = dataManager.handleSSERealtimeUpdate(sseData);
    
    // 应该返回更新后的数据（因为视口在热数据范围）
    expect(result).not.toBeNull();
    expect(Array.isArray(result)).toBe(true);
  });

  test('数据库更新处理', async () => {
    const hotCacheManager = (dataManager as any).hotCacheManager;
    
    // 测试视口在冷数据范围的情况
    hotCacheManager.updateViewport(sixtyDaysAgo, sixtyDaysAgo + 3600000);
    
    const result = await dataManager.handleDatabaseUpdate();
    
    // 应该返回null（静默更新）
    expect(result).toBeNull();
    
    // 测试视口在热数据范围的情况
    hotCacheManager.updateViewport(now - 3600000, now);
    
    const result2 = await dataManager.handleDatabaseUpdate();
    
    // 应该返回数据
    expect(result2).not.toBeNull();
  });

  test('立即可用数据获取', () => {
    const hotCacheManager = (dataManager as any).hotCacheManager;
    
    // 添加一些热数据
    const hotData = generateMockData(thirtyDaysAgo, 100);
    hotCacheManager.incrementalUpdateHotData(hotData);
    
    // 设置视口
    hotCacheManager.updateViewport(thirtyDaysAgo, now);
    
    const immediateData = dataManager.getImmediatelyAvailableData();
    
    expect(Array.isArray(immediateData)).toBe(true);
    expect(immediateData.length).toBeGreaterThan(0);
  });

  test('缓存统计信息', () => {
    const stats = dataManager.getCacheStats();
    
    expect(stats).toHaveProperty('total');
    expect(stats).toHaveProperty('hot');
    expect(stats).toHaveProperty('cold');
    expect(stats).toHaveProperty('cacheType');
    expect(stats.cacheType).toBe('测试');
  });

  test('强制清理功能', () => {
    const hotCacheManager = (dataManager as any).hotCacheManager;
    
    // 添加一些数据
    const testData = generateMockData(thirtyDaysAgo, 50);
    hotCacheManager.incrementalUpdateHotData(testData);
    
    let stats = hotCacheManager.getCacheStats();
    expect(stats.total).toBe(50);
    
    // 强制清理
    dataManager.clearAll();
    
    stats = hotCacheManager.getCacheStats();
    expect(stats.total).toBe(0);
  });
});

console.log('缓存管理器测试文件已创建');
console.log('运行测试命令: npm test -- cacheManager.test.ts');
console.log('');
console.log('主要测试功能:');
console.log('1. 热数据缓存基本功能');
console.log('2. 智能清理功能');
console.log('3. 视口边界保护功能');
console.log('4. SSE实时数据更新');
console.log('5. 数据库更新处理');
console.log('6. 立即可用数据获取');
console.log('7. 缓存统计信息');
console.log('8. 强制清理功能');
