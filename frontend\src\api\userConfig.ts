import api from './config';

/**
 * 用户设置接口
 */
export interface UserSettings {
  enableRegistration: boolean;
  inviteCodeRequired: boolean;
  passwordMinLength: number;
  passwordRequireSpecialChar: boolean;
}

/**
 * 预测图设置接口
 */
export interface PredictionSettings {
  enableKlinePrediction: boolean;
  enableLinePrediction: boolean;
}

/**
 * 用户配置接口
 */
export interface UserConfig {
  userSettings: UserSettings;
  predictionSettings: PredictionSettings;
}

/**
 * API响应接口
 */
interface UserConfigResponse {
  success: boolean;
  config: UserConfig;
}

/**
 * 获取用户配置（需要认证）
 * @returns 用户配置
 */
export const getUserConfig = async (): Promise<UserConfig> => {
  try {
    const response = await api.get<UserConfigResponse>('/config/user');
    
    if (response.data.success) {
      return response.data.config;
    } else {
      throw new Error('获取用户配置失败');
    }
  } catch (error) {
    console.error('获取用户配置失败:', error);
    // 返回默认配置
    return {
      userSettings: {
        enableRegistration: true,
        inviteCodeRequired: false,
        passwordMinLength: 8,
        passwordRequireSpecialChar: false
      },
      predictionSettings: {
        enableKlinePrediction: true,
        enableLinePrediction: true
      }
    };
  }
};

/**
 * 获取预测图设置（从用户配置中提取）
 * @returns 预测图设置
 */
export const getPredictionSettings = async (): Promise<PredictionSettings> => {
  try {
    const config = await getUserConfig();
    return config.predictionSettings;
  } catch (error) {
    console.error('获取预测图设置失败:', error);
    // 返回默认设置
    return {
      enableKlinePrediction: true,
      enableLinePrediction: true
    };
  }
};

/**
 * 获取用户注册设置（从用户配置中提取）
 * @returns 用户注册设置
 */
export const getUserSettings = async (): Promise<UserSettings> => {
  try {
    const config = await getUserConfig();
    return config.userSettings;
  } catch (error) {
    console.error('获取用户注册设置失败:', error);
    // 返回默认设置
    return {
      enableRegistration: true,
      inviteCodeRequired: false,
      passwordMinLength: 8,
      passwordRequireSpecialChar: false
    };
  }
};

export default {
  getUserConfig,
  getPredictionSettings,
  getUserSettings
};
