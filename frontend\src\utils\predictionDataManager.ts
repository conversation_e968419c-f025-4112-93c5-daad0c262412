import { createUnifiedDataManager } from './cacheManager';
import { fetchPredictions, fetchPredictionLines } from '../services/api';

/**
 * 预测K线数据统一管理器
 * 继承UnifiedDataManager，实现预测K线数据的具体加载逻辑
 */
class PredictionKLineDataManager {
  private unifiedManager: any;
  
  constructor() {
    this.unifiedManager = createUnifiedDataManager('预测K线');
    
    // 重写冷数据加载器的fetchFromServer方法
    this.setupColdDataLoader();
  }
  
  private setupColdDataLoader() {
    // 获取冷数据加载器实例并重写fetchFromServer方法
    const coldDataLoader = (this.unifiedManager as any).coldDataLoader;
    
    // 重写fetchFromServer方法
    coldDataLoader.fetchFromServer = async (startTime: number, endTime: number): Promise<any[]> => {
      console.log(`[预测K线冷数据] 从服务器加载: ${new Date(startTime)} - ${new Date(endTime)}`);
      
      try {
        // 使用现有的API，传入endTime作为历史数据的结束时间
        const data = await fetchPredictions(endTime);
        
        if (data && Array.isArray(data)) {
          // 过滤出指定时间范围内的数据
          const filteredData = data.filter(item => {
            const itemTime = typeof item.time === 'string' ? parseInt(item.time) : Number(item.time);
            const itemTimeMs = itemTime * 1000; // 转换为毫秒
            return itemTimeMs >= startTime && itemTimeMs <= endTime;
          });
          
          console.log(`[预测K线冷数据] 服务器返回 ${data.length} 条，过滤后 ${filteredData.length} 条`);
          return filteredData;
        }
        
        return [];
      } catch (error) {
        console.error(`[预测K线冷数据] 加载失败:`, error);
        throw error;
      }
    };
    
    // 重写统一管理器的updateHotDataCacheSilently方法
    this.unifiedManager.updateHotDataCacheSilently = async (): Promise<void> => {
      try {
        console.log(`[预测K线热数据] 静默更新开始`);
        
        // 获取最新的热数据（最近30天）
        const latestHotData = await fetchPredictions();
        
        if (latestHotData && Array.isArray(latestHotData)) {
          // 使用智能清理的增量更新
          const hotCacheManager = (this.unifiedManager as any).hotCacheManager;
          const { cleanedItems } = hotCacheManager.incrementalUpdateHotData(latestHotData);
          
          console.log(`[预测K线热数据] 静默更新完成，智能清理保护了视口边界数据`);
        }
      } catch (error) {
        console.error(`[预测K线热数据] 静默更新失败:`, error);
        throw error;
      }
    };
  }
  
  // 处理数据库更新通知（预测数据没有SSE实时推送）
  async handleDatabaseUpdate(): Promise<any[] | null> {
    return await this.unifiedManager.handleDatabaseUpdate();
  }
  
  // 视口变化处理
  async handleViewportChange(startTime: number, endTime: number): Promise<any[]> {
    return await this.unifiedManager.handleViewportChange(startTime, endTime);
  }
  
  // 获取立即可用的数据
  getImmediatelyAvailableData(): any[] {
    return this.unifiedManager.getImmediatelyAvailableData();
  }
  
  // 初始化加载（兼容现有逻辑）
  async loadInitialData(): Promise<any[]> {
    try {
      console.log(`[预测K线数据] 初始化加载开始`);
      
      // 获取历史数据
      const historicalData = await fetchPredictions();
      
      if (historicalData && Array.isArray(historicalData) && historicalData.length > 0) {
        console.log(`[预测K线数据] 获取到 ${historicalData.length} 条历史数据`);
        
        // 使用智能增量更新
        const hotCacheManager = (this.unifiedManager as any).hotCacheManager;
        const { updatedData } = hotCacheManager.incrementalUpdateHotData(historicalData);
        
        console.log(`[预测K线数据] 初始化完成，缓存 ${updatedData.length} 条数据`);
        return updatedData;
      }
      
      return [];
    } catch (error) {
      console.error(`[预测K线数据] 初始化加载失败:`, error);
      throw error;
    }
  }
  
  // 加载更多历史数据（兼容现有逻辑）
  async loadMoreHistory(endTime?: number): Promise<any[]> {
    try {
      let actualEndTime: number;
      
      if (endTime) {
        actualEndTime = endTime;
      } else {
        // 从当前数据中计算最早时间
        const hotCacheManager = (this.unifiedManager as any).hotCacheManager;
        const currentData = hotCacheManager.getAllHotData();
        
        if (currentData.length === 0) {
          return [];
        }
        
        // 获取最早的时间戳作为endTime
        actualEndTime = Math.min(...currentData.map(item => {
          const time = typeof item.time === 'string' ? parseInt(item.time) : Number(item.time);
          return time * 1000; // 转换为毫秒
        }));
      }
      
      console.log(`[预测K线数据] 加载更多历史数据，endTime: ${new Date(actualEndTime).toISOString()}`);
      
      const moreData = await fetchPredictions(actualEndTime);
      
      if (moreData && Array.isArray(moreData) && moreData.length > 0) {
        console.log(`[预测K线数据] 获取到 ${moreData.length} 条更多数据`);
        
        // 获取当前所有数据
        const hotCacheManager = (this.unifiedManager as any).hotCacheManager;
        const existingData = hotCacheManager.getAllHotData();
        
        // 智能合并数据
        return this.smartMergeData(existingData, moreData);
      }
      
      return [];
    } catch (error) {
      console.error(`[预测K线数据] 加载更多历史数据失败:`, error);
      throw error;
    }
  }
  
  // 智能合并数据
  private smartMergeData(existingData: any[], newData: any[]): any[] {
    const uniqueMap = new Map();
    
    // 先添加现有数据
    existingData.forEach(item => {
      const timeKey = typeof item.time === 'string' ? item.time : item.time.toString();
      uniqueMap.set(timeKey, item);
    });
    
    // 再添加新数据，覆盖重复时间戳
    newData.forEach(item => {
      const timeKey = typeof item.time === 'string' ? item.time : item.time.toString();
      uniqueMap.set(timeKey, item);
    });
    
    // 排序并更新缓存
    const deduplicatedData = Array.from(uniqueMap.values()).sort((a, b) => {
      const timeA = typeof a.time === 'string' ? parseInt(a.time) : Number(a.time);
      const timeB = typeof b.time === 'string' ? parseInt(b.time) : Number(b.time);
      return timeA - timeB;
    });
    
    // 使用智能增量更新
    const hotCacheManager = (this.unifiedManager as any).hotCacheManager;
    const { updatedData } = hotCacheManager.incrementalUpdateHotData(deduplicatedData);
    
    return updatedData;
  }
  
  // 获取缓存统计信息
  getCacheStats() {
    return this.unifiedManager.getCacheStats();
  }
  
  // 强制清理所有缓存
  clearAll() {
    this.unifiedManager.clearAll();
  }
}

/**
 * 预测折线数据统一管理器
 * 继承UnifiedDataManager，实现预测折线数据的具体加载逻辑
 */
class PredictionLineDataManager {
  private unifiedManager: any;
  private readonly DEFAULT_LIMIT = 100;
  
  constructor() {
    this.unifiedManager = createUnifiedDataManager('预测折线');
    
    // 重写冷数据加载器的fetchFromServer方法
    this.setupColdDataLoader();
  }
  
  private setupColdDataLoader() {
    // 获取冷数据加载器实例并重写fetchFromServer方法
    const coldDataLoader = (this.unifiedManager as any).coldDataLoader;
    
    // 重写fetchFromServer方法
    coldDataLoader.fetchFromServer = async (startTime: number, endTime: number): Promise<any[]> => {
      console.log(`[预测折线冷数据] 从服务器加载: ${new Date(startTime)} - ${new Date(endTime)}`);
      
      try {
        // 使用现有的API，传入endTime作为历史数据的结束时间
        const data = await fetchPredictionLines(endTime, this.DEFAULT_LIMIT);
        
        if (data && Array.isArray(data)) {
          // 过滤出指定时间范围内的数据
          const filteredData = data.filter(item => {
            const itemTime = typeof item.time === 'string' ? parseInt(item.time) : Number(item.time);
            const itemTimeMs = itemTime * 1000; // 转换为毫秒
            return itemTimeMs >= startTime && itemTimeMs <= endTime;
          });
          
          console.log(`[预测折线冷数据] 服务器返回 ${data.length} 条，过滤后 ${filteredData.length} 条`);
          return filteredData;
        }
        
        return [];
      } catch (error) {
        console.error(`[预测折线冷数据] 加载失败:`, error);
        throw error;
      }
    };
    
    // 重写统一管理器的updateHotDataCacheSilently方法
    this.unifiedManager.updateHotDataCacheSilently = async (): Promise<void> => {
      try {
        console.log(`[预测折线热数据] 静默更新开始`);
        
        // 获取最新的热数据（最近30天）
        const latestHotData = await fetchPredictionLines(undefined, this.DEFAULT_LIMIT);
        
        if (latestHotData && Array.isArray(latestHotData)) {
          // 使用智能清理的增量更新
          const hotCacheManager = (this.unifiedManager as any).hotCacheManager;
          const { cleanedItems } = hotCacheManager.incrementalUpdateHotData(latestHotData);
          
          console.log(`[预测折线热数据] 静默更新完成，智能清理保护了视口边界数据`);
        }
      } catch (error) {
        console.error(`[预测折线热数据] 静默更新失败:`, error);
        throw error;
      }
    };
  }
  
  // 其他方法与PredictionKLineDataManager类似，但使用fetchPredictionLines API
  // 为了保持代码简洁，这里省略重复的方法实现
  // 实际使用时可以通过继承或组合的方式复用代码
  
  async handleDatabaseUpdate(): Promise<any[] | null> {
    return await this.unifiedManager.handleDatabaseUpdate();
  }
  
  async loadInitialData(): Promise<any[]> {
    try {
      const historicalData = await fetchPredictionLines(undefined, this.DEFAULT_LIMIT);
      if (historicalData && Array.isArray(historicalData) && historicalData.length > 0) {
        const hotCacheManager = (this.unifiedManager as any).hotCacheManager;
        const { updatedData } = hotCacheManager.incrementalUpdateHotData(historicalData);
        return updatedData;
      }
      return [];
    } catch (error) {
      console.error(`[预测折线数据] 初始化加载失败:`, error);
      throw error;
    }
  }
  
  getCacheStats() {
    return this.unifiedManager.getCacheStats();
  }
  
  clearAll() {
    this.unifiedManager.clearAll();
  }
}

// 导出单例实例
export const predictionKLineDataManager = new PredictionKLineDataManager();
export const predictionLineDataManager = new PredictionLineDataManager();
