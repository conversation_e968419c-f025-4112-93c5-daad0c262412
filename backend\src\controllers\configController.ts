import { Request, Response } from 'express';
import systemSettingsService from '../services/systemSettingsService';

/**
 * 获取公共配置信息
 * 仅返回网站基本信息等真正需要公开的配置
 * GET /api/config
 */
export const getPublicConfig = async (req: Request, res: Response) => {
  try {
    // 获取网站基本信息
    const settings = await systemSettingsService.getSystemSettings();

    // 仅返回真正需要公开的配置
    res.json({
      success: true,
      config: {
        siteInfo: {
          siteName: settings.siteInfo.siteName,
          siteDescription: settings.siteInfo.siteDescription,
          siteKeywords: settings.siteInfo.siteKeywords,
          copyright: settings.siteInfo.copyright,
          socialLinks: settings.siteInfo.socialLinks,
          defaultLanguage: settings.siteInfo.defaultLanguage
        },
        // 水印配置 - 用于前端显示
        watermark: {
          siteDomain: settings.securitySettings.siteDomain || ''
        }
      }
    });
  } catch (error) {
    console.error('获取公共配置错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 获取用户配置信息（需要认证）
 * 返回用户注册相关设置和预测图设置
 * GET /api/config/user
 */
export const getUserConfig = async (req: Request, res: Response) => {
  try {
    // 获取系统设置
    const settings = await systemSettingsService.getSystemSettings();

    // 返回用户相关配置（需要认证才能访问）
    res.json({
      success: true,
      config: {
        // 用户注册相关设置（仅返回前端需要的部分）
        userSettings: {
          enableRegistration: settings.userSettings.enableRegistration,
          inviteCodeRequired: settings.userSettings.inviteCodeRequired,
          passwordMinLength: settings.userSettings.passwordMinLength,
          passwordRequireSpecialChar: settings.userSettings.passwordRequireSpecialChar
        },
        // 预测图显示设置
        predictionSettings: {
          enableKlinePrediction: settings.securitySettings.enableKlinePrediction,
          enableLinePrediction: settings.securitySettings.enableLinePrediction
        }
      }
    });
  } catch (error) {
    console.error('获取用户配置错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

export default {
  getPublicConfig,
  getUserConfig
};
