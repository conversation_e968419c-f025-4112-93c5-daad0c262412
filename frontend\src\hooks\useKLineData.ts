/**
 * K线数据获取和管理Hook
 * 
 * 该Hook负责K线数据的获取、更新和历史数据加载
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { fetchThirtyMinKlineData } from '../services/api';
import { KLineData } from '../types/chartTypes';
import { createCacheManager } from '../utils/cacheManager';
import { UnifiedDataManager } from '../utils/unifiedDataManager';
import { useSSEConnection } from './useSSEConnection';

interface KLineDataState {
  data: KLineData[];
  isLoading: boolean;
  isLoadingMoreCandles: boolean;
  error: string | null;
}

// 视口变化处理函数类型
type ViewportChangeHandler = (startTime: number, endTime: number) => Promise<void>;

/**
 * K线数据获取和管理Hook
 * 使用SSE实时推送获取K线数据
 * @returns K线数据状态和操作方法
 */
export const useKLineData = () => {
  // 新的统一数据管理器
  const dataManagerRef = useRef(new UnifiedDataManager('实时K线'));
  const dataManager = dataManagerRef.current;

  // 保留旧的缓存管理器用于兼容性（逐步迁移）
  const cacheManagerRef = useRef(createCacheManager('实时K线'));
  const cacheManager = cacheManagerRef.current;

  // 状态管理
  const [state, setState] = useState<KLineDataState>({
    data: [],
    isLoading: true,
    isLoadingMoreCandles: false,
    error: null
  });

  // 引用存储
  const isRefreshingRef = useRef<boolean>(false);

  // 使用统一SSE连接获取实时K线数据
  const {
    klineData: sseKlineData,
    connectionStatus,
    error: sseError,
    databaseUpdateTrigger
  } = useSSEConnection();

  // 初始加载历史K线数据
  const loadData = useCallback(async (isInitialLoad: boolean = true) => {
    if (isRefreshingRef.current) return;

    isRefreshingRef.current = true;

    // 只有初始加载才显示加载状态
    if (isInitialLoad) {
      setState(prev => ({ ...prev, isLoading: true }));
    }

    try {
      // 只获取历史数据，实时数据通过SSE获取
      const historicalData = await fetchThirtyMinKlineData();
      if (historicalData && Array.isArray(historicalData) && historicalData.length > 0) {
        console.log(`获取到 ${historicalData.length} 条历史K线数据${isInitialLoad ? '' : ' (数据库更新)'}`);

        // 智能合并数据，保持现有SSE实时数据
        setState(prev => {
          const existingData = prev.data;

          // 如果是初始加载且没有现有数据，直接使用历史数据
          if (isInitialLoad && existingData.length === 0) {
            const cachedData = cacheManager.addData(historicalData);
            console.log('初始加载K线缓存统计:', cacheManager.getCacheStats());
            return {
              ...prev,
              data: cachedData,
              error: null
            };
          }

          // 智能合并：数据库数据优先，保持SSE实时数据
          const uniqueMap = new Map();

          // 先添加历史数据（数据库数据）
          historicalData.forEach(item => {
            const timeKey = typeof item.time === 'string' ? item.time : item.time.toString();
            uniqueMap.set(timeKey, {
              ...item,
              priority: 'database'
            });
          });

          // 再添加现有数据，SSE实时数据优先级更高
          existingData.forEach(item => {
            const timeKey = typeof item.time === 'string' ? item.time : item.time.toString();
            const existingItem = uniqueMap.get(timeKey);

            // 如果现有数据是SSE实时数据，则优先使用（保持最新价格）
            if ((item as any).fromBinance && existingItem) {
              uniqueMap.set(timeKey, {
                ...item,
                priority: 'sse-realtime'
              });
            } else if (!uniqueMap.has(timeKey)) {
              uniqueMap.set(timeKey, {
                ...item,
                priority: 'existing'
              });
            }
          });

          // 转换为数组并排序，移除priority字段
          const mergedData = Array.from(uniqueMap.values()).map(item => {
            const { priority, ...cleanItem } = item;
            return cleanItem;
          }) as KLineData[];

          mergedData.sort((a, b) => {
            const timeA = typeof a.time === 'string' ? parseInt(a.time) : Number(a.time);
            const timeB = typeof b.time === 'string' ? parseInt(b.time) : Number(b.time);
            return timeA - timeB;
          });

          // 更新缓存管理器
          cacheManager.clearAll();
          const cachedData = cacheManager.addData(mergedData);

          console.log(`数据合并完成，共有 ${cachedData.length} 条K线数据`);

          return {
            ...prev,
            data: cachedData,
            error: null
          };
        });
      } else {
        console.warn('获取到的历史K线数据为空或无效');
      }
    } catch (err) {
      console.error('历史数据加载失败:', err);
      // 只有初始加载才显示错误，数据库更新失败不干扰用户
      if (isInitialLoad) {
        setState(prev => ({
          ...prev,
          error: '获取历史K线数据失败'
        }));
      }
    } finally {
      // 只有初始加载才更新加载状态
      if (isInitialLoad) {
        setState(prev => ({ ...prev, isLoading: false }));
      }
      isRefreshingRef.current = false;
    }
  }, [cacheManager]);

  // 处理SSE实时数据更新
  const handleSSEDataUpdate = useCallback((sseData: KLineData[]) => {
    if (!sseData || sseData.length === 0) return;

    console.log(`收到SSE实时K线数据更新: ${sseData.length} 条数据`);

    setState(prev => {
      // 1. 获取当前所有有效缓存数据
      const existingData = cacheManager.getAllValidData();

      // 2. 创建一个Map用于去重，优化数据合并策略
      const uniqueMap = new Map();

      // 先处理现有数据（历史数据优先）
      existingData.forEach(item => {
        const timeKey = typeof item.time === 'string' ? item.time : item.time.toString();
        uniqueMap.set(timeKey, {
          ...item,
          priority: (item as any).fromDatabase ? 'database' : 'existing'
        });
      });

      // 再处理SSE实时数据，SSE数据始终可以更新价格
      sseData.forEach(item => {
        const timeKey = typeof item.time === 'string' ? item.time : item.time.toString();

        // 修正的数据合并策略：SSE实时数据可以更新任何时间点的价格
        // 这确保了实时价格更新能够正常工作
        uniqueMap.set(timeKey, {
          ...item,
          priority: 'sse',
          fromBinance: true // 标记为来自币安的实时数据
        });
      });

      // 转换为数组并排序，移除priority字段
      const combinedData = Array.from(uniqueMap.values()).map(item => {
        const { priority, ...cleanItem } = item;
        return cleanItem;
      }) as KLineData[];

      combinedData.sort((a, b) => {
        const timeA = typeof a.time === 'string' ? parseInt(a.time) : Number(a.time);
        const timeB = typeof b.time === 'string' ? parseInt(b.time) : Number(b.time);
        return timeA - timeB;
      });

      // 3. 将合并后的数据添加到缓存管理器
      const cachedData = cacheManager.addData(combinedData);

      console.log(`SSE数据合并后共有 ${cachedData.length} 条K线数据`);
      console.log('K线缓存统计:', cacheManager.getCacheStats());

      return {
        ...prev,
        data: cachedData,
        error: null
      };
    });
  }, [cacheManager]);

  // 加载更多历史K线数据
  const loadMoreHistory = useCallback(async (oldestTime: number) => {
    setState(prev => {
      if (prev.isLoadingMoreCandles) return prev;
      return { ...prev, isLoadingMoreCandles: true };
    });
    
    console.log(`加载更早的30分钟K线数据，时间戳: ${oldestTime}, ${new Date(oldestTime * 1000).toISOString()}`);
    
    try {
      // 获取指定时间之前的30分钟K线数据
      // 后端API需要毫秒格式的时间戳，而图表传来的oldestTime是秒级时间戳
      const olderCandles = await fetchThirtyMinKlineData(oldestTime * 1000);
      
      if (olderCandles && Array.isArray(olderCandles) && olderCandles.length > 0) {
        console.log(`获取到 ${olderCandles.length} 条更早的30分钟K线数据`);
        
        // 合并新旧数据并去重
        setState(prev => {
          // 1. 获取当前所有有效缓存数据
          const existingData = cacheManager.getAllValidData();

          // 2. 创建一个Map用于去重
          const uniqueMap = new Map();

          // 先处理当前数据
          existingData.forEach(item => {
            const timeKey = typeof item.time === 'string' ? item.time : item.time.toString();
            uniqueMap.set(timeKey, item);
          });

          // 再处理新数据，有相同时间则新数据覆盖旧数据
          olderCandles.forEach(item => {
            const timeKey = typeof item.time === 'string' ? item.time : item.time.toString();
            uniqueMap.set(timeKey, item);
          });

          // 转换为数组并排序
          const combinedData = Array.from(uniqueMap.values()) as KLineData[];
          combinedData.sort((a, b) => {
            const timeA = typeof a.time === 'string' ? parseInt(a.time) : Number(a.time);
            const timeB = typeof b.time === 'string' ? parseInt(b.time) : Number(b.time);
            return timeA - timeB;
          });

          // 3. 添加到缓存管理器
          const cachedData = cacheManager.addData(combinedData);

          console.log(`合并后共有 ${cachedData.length} 条30分钟K线数据`);
          return {
            ...prev,
            data: cachedData,
            isLoadingMoreCandles: false
          };
        });
      } else {
        console.log('没有更多历史30分钟K线数据了');
        setState(prev => ({ ...prev, isLoadingMoreCandles: false }));
      }
    } catch (err) {
      console.error('加载历史30分钟K线数据失败:', err);
      setState(prev => ({ ...prev, isLoadingMoreCandles: false }));
    }
  }, [cacheManager]);

  // 新的SSE数据处理逻辑
  const handleNewSSEDataUpdate = useCallback((sseData: KLineData[]) => {
    if (!sseData || sseData.length === 0) return;

    console.log(`[新SSE处理] 收到 ${sseData.length} 条实时K线数据`);

    const updatedData = dataManager.handleSSERealtimeUpdate(sseData);

    // 只有返回数据时才更新UI（视口在热数据范围）
    if (updatedData && updatedData.length > 0) {
      setState(prev => ({
        ...prev,
        data: updatedData,
        error: null
      }));
    }
  }, [dataManager]);

  // 处理SSE数据更新（保留旧逻辑用于对比）
  useEffect(() => {
    if (sseKlineData && sseKlineData.length > 0) {
      // 使用新的处理逻辑
      handleNewSSEDataUpdate(sseKlineData);

      // 暂时保留旧逻辑用于对比（后续可删除）
      // handleSSEDataUpdate(sseKlineData);
    }
  }, [sseKlineData, handleNewSSEDataUpdate]);

  // 处理SSE连接状态和错误
  useEffect(() => {
    if (sseError) {
      setState(prev => ({
        ...prev,
        error: sseError
      }));
    } else if (connectionStatus === 'connected') {
      setState(prev => ({
        ...prev,
        error: null
      }));
    }
  }, [sseError, connectionStatus]);

  // 首次加载历史数据
  useEffect(() => {
    loadData();
  }, [loadData]);

  // 新的数据库更新处理逻辑
  const handleNewDatabaseUpdate = useCallback(async () => {
    console.log('[新数据库更新] 收到更新通知');

    try {
      const updatedData = await dataManager.handleDatabaseUpdate();

      // 只有返回数据时才更新UI
      if (updatedData && updatedData.length > 0) {
        setState(prev => ({
          ...prev,
          data: updatedData,
          error: null
        }));
      }
    } catch (error) {
      console.error('[新数据库更新] 处理失败:', error);
      setState(prev => ({
        ...prev,
        error: (error as Error).message || '数据库更新处理失败'
      }));
    }
  }, [dataManager]);

  // 监听数据库更新通知
  useEffect(() => {
    if (databaseUpdateTrigger > 0) {
      // 使用新的处理逻辑
      handleNewDatabaseUpdate();

      // 暂时保留旧逻辑用于对比（后续可删除）
      // console.log('收到数据库更新通知，智能合并新数据');
      // loadData(false); // 非初始加载，使用智能合并
    }
  }, [databaseUpdateTrigger, handleNewDatabaseUpdate]);

  // 新的视口变化处理函数
  const handleViewportChange: ViewportChangeHandler = useCallback(async (startTime: number, endTime: number) => {
    console.log(`[视口变化] K线数据 ${new Date(startTime)} - ${new Date(endTime)}`);

    // 先显示立即可用的数据
    const immediateData = dataManager.getImmediatelyAvailableData();

    if (immediateData.length > 0) {
      console.log(`[视口变化] 立即显示 ${immediateData.length} 条可用数据`);
      setState(prev => ({
        ...prev,
        data: immediateData,
        isLoading: true, // 标记为加载中，可能还有更多数据
        error: null
      }));
    } else {
      console.log(`[视口变化] 无立即可用数据，显示加载状态`);
      setState(prev => ({
        ...prev,
        isLoading: true,
        error: null
      }));
    }

    try {
      // 加载完整数据
      const completeData = await dataManager.handleViewportChange(startTime, endTime);

      // 检查是否有新数据
      if (completeData.length !== immediateData.length ||
          !arraysEqual(completeData, immediateData)) {
        console.log(`[视口变化] 更新为完整数据: ${completeData.length} 条`);
        setState(prev => ({
          ...prev,
          data: completeData,
          isLoading: false,
          error: null
        }));
      } else {
        console.log(`[视口变化] 数据无变化，仅更新加载状态`);
        setState(prev => ({
          ...prev,
          isLoading: false
        }));
      }

    } catch (error) {
      console.error(`[视口变化] 加载失败:`, error);
      setState(prev => ({
        ...prev,
        error: (error as Error).message || '视口数据加载失败',
        isLoading: false
      }));
    }
  }, [dataManager]);

  // 数组比较辅助函数
  const arraysEqual = (a: any[], b: any[]): boolean => {
    if (a.length !== b.length) return false;
    return a.every((item, index) => {
      const aTime = typeof item.time === 'string' ? item.time : item.time.toString();
      const bTime = typeof b[index].time === 'string' ? b[index].time : b[index].time.toString();
      return aTime === bTime;
    });
  };

  // 新的初始化逻辑
  useEffect(() => {
    // 初始加载最近24小时的数据
    const now = Date.now();
    const oneDay = 24 * 60 * 60 * 1000;
    handleViewportChange(now - oneDay, now);
  }, [handleViewportChange]);

  return {
    candleData: state.data,
    isLoading: state.isLoading,
    isLoadingMoreCandles: state.isLoadingMoreCandles,
    error: state.error,
    loadData,
    loadMoreHistory,
    // 新增的视口变化处理函数
    onViewportChange: handleViewportChange,
    // SSE相关状态
    connectionStatus,
    sseError
  };
}; 