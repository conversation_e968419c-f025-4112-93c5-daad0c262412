/**
 * K线数据获取和管理Hook
 * 
 * 该Hook负责K线数据的获取、更新和历史数据加载
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { fetchThirtyMinKlineData } from '../services/api';
import { KLineData } from '../types/chartTypes';
import { klineDataManager } from '../utils/klineDataManager';
import { useSSEConnection } from './useSSEConnection';

interface KLineDataState {
  data: KLineData[];
  isLoading: boolean;
  isLoadingMoreCandles: boolean;
  error: string | null;
}

/**
 * K线数据获取和管理Hook
 * 使用SSE实时推送获取K线数据
 * @returns K线数据状态和操作方法
 */
export const useKLineData = () => {
  // 状态管理
  const [state, setState] = useState<KLineDataState>({
    data: [],
    isLoading: true,
    isLoadingMoreCandles: false,
    error: null
  });

  // 引用存储
  const isRefreshingRef = useRef<boolean>(false);

  // 使用统一SSE连接获取实时K线数据
  const {
    klineData: sseKlineData,
    connectionStatus,
    error: sseError,
    databaseUpdateTrigger
  } = useSSEConnection();

  // 初始加载历史K线数据
  const loadData = useCallback(async (isInitialLoad: boolean = true) => {
    if (isRefreshingRef.current) return;

    isRefreshingRef.current = true;

    // 只有初始加载才显示加载状态
    if (isInitialLoad) {
      setState(prev => ({ ...prev, isLoading: true }));
    }

    try {
      if (isInitialLoad) {
        // 初始加载使用新的数据管理器
        const initialData = await klineDataManager.loadInitialData();
        console.log('初始加载K线缓存统计:', klineDataManager.getCacheStats());

        setState(prev => ({
          ...prev,
          data: initialData,
          error: null
        }));
      } else {
        // 数据库更新处理
        console.log('收到数据库更新通知，使用智能合并');
        const updatedData = await klineDataManager.handleDatabaseUpdate();

        if (updatedData && updatedData.length > 0) {
          setState(prev => ({
            ...prev,
            data: updatedData,
            error: null
          }));
        }
      }
    } catch (err) {
      console.error('历史数据加载失败:', err);
      // 只有初始加载才显示错误，数据库更新失败不干扰用户
      if (isInitialLoad) {
        setState(prev => ({
          ...prev,
          error: '获取历史K线数据失败'
        }));
      }
    } finally {
      // 只有初始加载才更新加载状态
      if (isInitialLoad) {
        setState(prev => ({ ...prev, isLoading: false }));
      }
      isRefreshingRef.current = false;
    }
  }, []);

  // 处理SSE实时数据更新
  const handleSSEDataUpdate = useCallback((sseData: KLineData[]) => {
    if (!sseData || sseData.length === 0) return;

    console.log(`收到SSE实时K线数据更新: ${sseData.length} 条数据`);

    // 使用新的数据管理器处理SSE更新
    const updatedData = klineDataManager.handleSSERealtimeUpdate(sseData);

    // 只有返回数据时才更新UI（视口在热数据范围）
    if (updatedData && updatedData.length > 0) {
      setState(prev => ({
        ...prev,
        data: updatedData,
        error: null
      }));

      console.log(`SSE数据更新后共有 ${updatedData.length} 条K线数据`);
      console.log('K线缓存统计:', klineDataManager.getCacheStats());
    }
  }, []);

  // 加载更多历史K线数据
  const loadMoreHistory = useCallback(async (oldestTime: number) => {
    setState(prev => {
      if (prev.isLoadingMoreCandles) return prev;
      return { ...prev, isLoadingMoreCandles: true };
    });

    console.log(`加载更早的30分钟K线数据，时间戳: ${oldestTime}, ${new Date(oldestTime * 1000).toISOString()}`);

    try {
      // 使用新的数据管理器加载更多历史数据
      const combinedData = await klineDataManager.loadMoreHistory(oldestTime);

      if (combinedData && combinedData.length > 0) {
        console.log(`合并后共有 ${combinedData.length} 条30分钟K线数据`);

        setState(prev => ({
          ...prev,
          data: combinedData,
          isLoadingMoreCandles: false
        }));
      } else {
        console.log('没有更多历史30分钟K线数据了');
        setState(prev => ({ ...prev, isLoadingMoreCandles: false }));
      }
    } catch (err) {
      console.error('加载历史30分钟K线数据失败:', err);
      setState(prev => ({ ...prev, isLoadingMoreCandles: false }));
    }
  }, []);

  // 处理SSE数据更新
  useEffect(() => {
    if (sseKlineData && sseKlineData.length > 0) {
      handleSSEDataUpdate(sseKlineData);
    }
  }, [sseKlineData, handleSSEDataUpdate]);

  // 处理SSE连接状态和错误
  useEffect(() => {
    if (sseError) {
      setState(prev => ({
        ...prev,
        error: sseError
      }));
    } else if (connectionStatus === 'connected') {
      setState(prev => ({
        ...prev,
        error: null
      }));
    }
  }, [sseError, connectionStatus]);

  // 首次加载历史数据
  useEffect(() => {
    loadData();
  }, [loadData]);

  // 监听数据库更新通知，智能合并新数据
  useEffect(() => {
    if (databaseUpdateTrigger > 0) {
      console.log('收到数据库更新通知，智能合并新数据');
      loadData(false); // 非初始加载，使用智能合并
    }
  }, [databaseUpdateTrigger, loadData]);

  return {
    candleData: state.data,
    isLoading: state.isLoading,
    isLoadingMoreCandles: state.isLoadingMoreCandles,
    error: state.error,
    loadData,
    loadMoreHistory,
    // SSE相关状态
    connectionStatus,
    sseError
  };
}; 