import { Router } from 'express';
import predictionRoutes from './predictionRoutes';
import thirtyMinKlineRoutes from './thirtyMinKlineRoutes';
import solarTermPredictionRoutes from './solarTermPredictionRoutes';
import authRoutes from './auth';
import inviteRoutes from './inviteRoutes';
import adminRoutes from './adminRoutes';
import subscriptionRoutes from './subscriptionRoutes';
import publicRoutes from './publicRoutes';
import feedbackRoutes from './feedbackRoutes';
import notificationRoutes from './notificationRoutes';
import documentRoutes from './documentRoutes';
import klineSSERoutes from './klineSSERoutes';

import configController from '../controllers/configController';
import authenticate from '../middlewares/authMiddleware';

// 创建主路由实例
const router = Router();

// 注册K线数据相关的API路由


// 注册预测数据相关的API路由
router.use('/predictions', predictionRoutes);

// 注册30分钟K线数据相关的API路由
router.use('/thirty-min-klines', thirtyMinKlineRoutes);

// 注册节气预测折线API路由
router.use('/solar-term-predictions', solarTermPredictionRoutes);

// 注册用户认证相关的API路由
router.use('/auth', authRoutes);

// 注册邀请码相关的API路由
router.use('/invites', inviteRoutes);

// 注册管理员相关的API路由
router.use('/admin', adminRoutes);

// 注册订阅相关的API路由
router.use('/subscription', subscriptionRoutes);

// 注册公共API路由
router.use('/public', publicRoutes);

// 注册用户反馈相关的API路由
router.use('/feedback', feedbackRoutes);

// 注册用户通知相关的API路由
router.use('/notifications', notificationRoutes);

// 注册文档相关的API路由
router.use('/docs', documentRoutes);

// 注册K线SSE相关的API路由
router.use('/kline-sse', klineSSERoutes);

// 公共配置接口（无需认证）
router.get('/config', configController.getPublicConfig);

// 用户配置接口（需要认证）
router.get('/config/user', authenticate, configController.getUserConfig);

// 健康检查路由
// GET /api/health
router.get('/health', (_req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});




// 导出主路由
export default router;