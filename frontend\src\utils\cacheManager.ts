// 热数据缓存项接口
interface HotCacheItem {
  data: any;
  timestamp: number;  // 缓存时间戳
  itemTime: number;   // 数据项的时间戳
}

// 冷数据缓存项接口
interface ColdCacheItem {
  data: any[];
  timestamp: number;
  lastAccess: number;
  range: [number, number];
}

// 视口信息接口
interface ViewportInfo {
  startTime: number;
  endTime: number;
  isInHotRange: boolean;      // 视口是否包含热数据
  isInColdRange: boolean;     // 视口是否包含冷数据
  isFullyInHotRange: boolean; // 视口是否完全在热数据范围
  isFullyInColdRange: boolean;// 视口是否完全在冷数据范围
}

// 缓存配置
const HOT_DATA_THRESHOLD = 30 * 24 * 60 * 60 * 1000; // 30天热数据
const COLD_DATA_TTL = 5 * 60 * 1000; // 5分钟冷数据TTL

/**
 * 热数据缓存管理器
 * 管理最近30天的数据，支持智能增量更新和视口边界保护
 */
class HotDataCacheManager {
  private readonly HOT_DATA_THRESHOLD = HOT_DATA_THRESHOLD;
  private hotCache = new Map<string, HotCacheItem>();
  private currentViewport: ViewportInfo | null = null;
  private cacheType: string;

  constructor(cacheType: string) {
    this.cacheType = cacheType;
  }

  // 更新视口信息
  updateViewport(startTime: number, endTime: number) {
    const hotThreshold = Date.now() - this.HOT_DATA_THRESHOLD;

    this.currentViewport = {
      startTime,
      endTime,
      isInHotRange: endTime >= hotThreshold,
      isInColdRange: startTime < hotThreshold,
      isFullyInHotRange: startTime >= hotThreshold,
      isFullyInColdRange: endTime < hotThreshold
    };

    console.log(`[${this.cacheType}视口更新] ${new Date(startTime)} - ${new Date(endTime)}`);
    console.log(`[${this.cacheType}视口状态] 跨越边界: ${this.currentViewport.isInHotRange && this.currentViewport.isInColdRange}`);
  }

  // 智能增量更新（核心逻辑）
  incrementalUpdateHotData(newData: any[]): { updatedData: any[], cleanedItems: any[] } {
    const now = Date.now();
    const hotThreshold = now - this.HOT_DATA_THRESHOLD;
    const cleanedItems: any[] = [];

    // 1. 增量更新热数据
    newData.forEach(item => {
      const itemTime = this.getItemTime(item);
      const key = this.getItemKey(item);

      if (itemTime >= hotThreshold) {
        const existing = this.hotCache.get(key);
        this.hotCache.set(key, {
          data: existing ? this.mergeData(existing.data, item) : item,
          timestamp: now,
          itemTime: itemTime
        });
      }
    });

    // 2. 智能清理过期数据（关键改进）
    this.hotCache.forEach((item, key) => {
      if (item.itemTime < hotThreshold) {
        if (this.shouldProtectFromCleanup(item)) {
          console.log(`[${this.cacheType}智能清理] 保护视口内数据: ${new Date(item.itemTime)}`);
          // 不清理，保留在缓存中
        } else {
          cleanedItems.push({
            key,
            itemTime: item.itemTime,
            data: item.data
          });
          this.hotCache.delete(key);
        }
      }
    });

    if (cleanedItems.length > 0) {
      console.log(`[${this.cacheType}智能清理] 清理了 ${cleanedItems.length} 条过期数据`);
    }

    return {
      updatedData: this.getAllHotData(),
      cleanedItems
    };
  }
  
  // 数据保护判断（防止视口边界断层）
  private shouldProtectFromCleanup(item: HotCacheItem): boolean {
    if (!this.currentViewport) {
      return false; // 无视口信息，不保护
    }

    // 只有当视口跨越冷热边界时才需要保护
    if (!this.currentViewport.isInColdRange || !this.currentViewport.isInHotRange) {
      return false; // 视口不跨边界，不需要保护
    }

    // 检查数据是否在视口范围内
    return (
      item.itemTime >= this.currentViewport.startTime &&
      item.itemTime <= this.currentViewport.endTime
    );
  }

  // 获取视口范围内的热数据
  getHotDataInViewport(): any[] {
    if (!this.currentViewport) return [];

    const { startTime, endTime } = this.currentViewport;
    const hotThreshold = Date.now() - this.HOT_DATA_THRESHOLD;

    return Array.from(this.hotCache.values())
      .filter(item => {
        const itemTime = item.itemTime;
        return itemTime >= Math.max(startTime, hotThreshold) && itemTime <= endTime;
      })
      .map(item => item.data)
      .sort((a, b) => this.getItemTime(a) - this.getItemTime(b));
  }

  // 获取所有热数据
  getAllHotData(): any[] {
    return Array.from(this.hotCache.values())
      .map(item => item.data)
      .sort((a, b) => this.getItemTime(a) - this.getItemTime(b));
  }
  
  // 获取缓存统计信息
  getCacheStats() {
    const now = Date.now();
    const hotThreshold = now - this.HOT_DATA_THRESHOLD;
    let hotCount = 0;
    let coldCount = 0;

    this.hotCache.forEach((item) => {
      if (item.itemTime >= hotThreshold) {
        hotCount++;
      } else {
        coldCount++;
      }
    });

    return {
      total: this.hotCache.size,
      hot: hotCount,
      cold: coldCount,
      cacheType: this.cacheType
    };
  }

  // 强制清理所有缓存
  clearAll() {
    this.hotCache.clear();
    console.log(`[${this.cacheType}缓存清理] 强制清理所有热数据缓存`);
  }

  private mergeData(existing: any, newItem: any): any {
    // 实时数据优先，保留最新的价格信息
    return { ...existing, ...newItem };
  }
  
  private getItemKey(item: any): string {
    return typeof item.time === 'string' ? item.time : item.time.toString();
  }

  private getItemTime(item: any): number {
    return typeof item.time === 'string' ? parseInt(item.time) : Number(item.time);
  }
}

/**
 * 冷数据按需加载器
 * 管理30天前的历史数据，5分钟临时缓存，支持缺失检测和补偿
 */
class ColdDataLoader {
  private coldDataCache = new Map<string, ColdCacheItem>();
  private readonly COLD_DATA_TTL = COLD_DATA_TTL;
  private loadingPromises = new Map<string, Promise<any[]>>(); // 防止重复加载
  private cacheType: string;

  constructor(cacheType: string) {
    this.cacheType = cacheType;
  }

  // 按需加载冷数据（增强版）
  async loadColdData(startTime: number, endTime: number): Promise<any[]> {
    const cacheKey = `${startTime}-${endTime}`;

    // 1. 检查缓存
    const cached = this.coldDataCache.get(cacheKey);
    if (cached && this.isCacheValid(cached)) {
      cached.lastAccess = Date.now();
      console.log(`[${this.cacheType}冷数据] 缓存命中: ${cached.data.length} 条数据`);
      return cached.data;
    }

    // 2. 检查是否正在加载中
    const existingPromise = this.loadingPromises.get(cacheKey);
    if (existingPromise) {
      console.log(`[${this.cacheType}冷数据] 等待正在进行的加载: ${cacheKey}`);
      return await existingPromise;
    }

    // 3. 检查是否有部分缓存可用
    const partialData = this.getPartialCachedData(startTime, endTime);
    const missingRanges = this.calculateMissingRanges(startTime, endTime, partialData);

    if (missingRanges.length === 0) {
      console.log(`[${this.cacheType}冷数据] 部分缓存完全覆盖请求范围`);
      return partialData;
    }

    // 4. 加载缺失的数据
    console.log(`[${this.cacheType}冷数据] 需要加载 ${missingRanges.length} 个缺失范围`);
    const loadPromise = this.loadMissingData(missingRanges, partialData);
    this.loadingPromises.set(cacheKey, loadPromise);

    try {
      const completeData = await loadPromise;

      // 缓存完整数据
      this.coldDataCache.set(cacheKey, {
        data: completeData,
        timestamp: Date.now(),
        lastAccess: Date.now(),
        range: [startTime, endTime]
      });

      this.cleanupExpiredColdData();
      return completeData;

    } finally {
      this.loadingPromises.delete(cacheKey);
    }
  }
  
  // 获取部分缓存数据
  private getPartialCachedData(startTime: number, endTime: number): any[] {
    const now = Date.now();
    let partialData: any[] = [];

    this.coldDataCache.forEach((item, key) => {
      if (now - item.lastAccess <= this.COLD_DATA_TTL) {
        const [cacheStart, cacheEnd] = item.range;

        // 检查范围重叠
        if (cacheStart < endTime && cacheEnd > startTime) {
          const filteredData = item.data.filter(dataItem => {
            const itemTime = this.getItemTime(dataItem);
            return itemTime >= startTime && itemTime <= endTime;
          });

          partialData = [...partialData, ...filteredData];
          console.log(`[${this.cacheType}部分缓存] 从缓存 ${key} 获取 ${filteredData.length} 条数据`);
        }
      }
    });

    return this.sortAndDeduplicate(partialData);
  }

  // 计算缺失的数据范围
  private calculateMissingRanges(
    startTime: number,
    endTime: number,
    existingData: any[]
  ): Array<[number, number]> {
    if (existingData.length === 0) {
      return [[startTime, endTime]];
    }

    const KLINE_INTERVAL = 30 * 60 * 1000; // 30分钟K线间隔
    const sortedData = existingData.sort((a, b) => this.getItemTime(a) - this.getItemTime(b));
    const missingRanges: Array<[number, number]> = [];

    // 检查开头是否缺失
    const firstDataTime = this.getItemTime(sortedData[0]);
    if (firstDataTime > startTime + KLINE_INTERVAL) {
      missingRanges.push([startTime, firstDataTime - KLINE_INTERVAL]);
    }

    // 检查中间是否有断层
    for (let i = 0; i < sortedData.length - 1; i++) {
      const currentTime = this.getItemTime(sortedData[i]);
      const nextTime = this.getItemTime(sortedData[i + 1]);
      const expectedNextTime = currentTime + KLINE_INTERVAL;

      if (nextTime > expectedNextTime + KLINE_INTERVAL) {
        missingRanges.push([expectedNextTime, nextTime - KLINE_INTERVAL]);
      }
    }

    // 检查结尾是否缺失
    const lastDataTime = this.getItemTime(sortedData[sortedData.length - 1]);
    if (lastDataTime < endTime - KLINE_INTERVAL) {
      missingRanges.push([lastDataTime + KLINE_INTERVAL, endTime]);
    }

    if (missingRanges.length > 0) {
      console.log(`[${this.cacheType}缺失检测] 发现 ${missingRanges.length} 个缺失范围:`);
      missingRanges.forEach((range, index) => {
        console.log(`[${this.cacheType}缺失${index + 1}] ${new Date(range[0])} - ${new Date(range[1])}`);
      });
    }

    return missingRanges;
  }

  // 加载缺失的数据
  private async loadMissingData(
    missingRanges: Array<[number, number]>,
    existingData: any[]
  ): Promise<any[]> {
    let allData = [...existingData];

    // 并行加载所有缺失范围
    const loadPromises = missingRanges.map(async ([rangeStart, rangeEnd]) => {
      try {
        console.log(`[${this.cacheType}缺失加载] 加载范围: ${new Date(rangeStart)} - ${new Date(rangeEnd)}`);
        const data = await this.fetchFromServer(rangeStart, rangeEnd);
        console.log(`[${this.cacheType}缺失加载] 加载完成: ${data.length} 条数据`);
        return data;
      } catch (error) {
        console.error(`[${this.cacheType}缺失加载] 失败: ${new Date(rangeStart)} - ${new Date(rangeEnd)}`, error);
        return [];
      }
    });

    const missingDataArrays = await Promise.all(loadPromises);

    // 合并所有数据
    missingDataArrays.forEach(missingData => {
      allData = [...allData, ...missingData];
    });

    return this.sortAndDeduplicate(allData);
  }

  // 获取立即可用的缓存数据（用于渐进式显示）
  getImmediatelyAvailableColdData(startTime: number, endTime: number): any[] {
    const partialData = this.getPartialCachedData(startTime, endTime);

    if (partialData.length > 0) {
      console.log(`[${this.cacheType}立即可用] 找到 ${partialData.length} 条缓存的冷数据`);
    } else {
      console.log(`[${this.cacheType}立即可用] 无可用的冷数据缓存`);
    }

    return partialData;
  }

  // 检查数据完整性
  isDataComplete(startTime: number, endTime: number, data: any[]): boolean {
    const missingRanges = this.calculateMissingRanges(startTime, endTime, data);
    return missingRanges.length === 0;
  }

  // 清理过期的冷数据缓存
  private cleanupExpiredColdData() {
    const now = Date.now();
    let cleanedCount = 0;

    this.coldDataCache.forEach((item, key) => {
      if (now - item.lastAccess > this.COLD_DATA_TTL) {
        this.coldDataCache.delete(key);
        cleanedCount++;
      }
    });

    if (cleanedCount > 0) {
      console.log(`[${this.cacheType}冷数据清理] 清理了 ${cleanedCount} 条过期缓存`);
    }
  }

  private isCacheValid(cached: ColdCacheItem): boolean {
    return Date.now() - cached.lastAccess <= this.COLD_DATA_TTL;
  }

  private async fetchFromServer(startTime: number, endTime: number): Promise<any[]> {
    // 这个方法需要在具体的实现中重写
    throw new Error(`${this.cacheType}: fetchFromServer 方法需要在具体实现中重写`);
  }

  private getItemTime(item: any): number {
    return typeof item.time === 'string' ? parseInt(item.time) : Number(item.time);
  }

  private sortAndDeduplicate(data: any[]): any[] {
    const uniqueMap = new Map();
    data.forEach(item => {
      const key = typeof item.time === 'string' ? item.time : item.time.toString();
      uniqueMap.set(key, item);
    });

    return Array.from(uniqueMap.values()).sort((a, b) =>
      this.getItemTime(a) - this.getItemTime(b)
    );
  }
}

/**
 * 统一数据管理器
 * 整合热数据缓存和冷数据加载，提供统一的数据管理接口
 */
class UnifiedDataManager {
  private hotCacheManager: HotDataCacheManager;
  private coldDataLoader: ColdDataLoader;

  constructor(cacheType: string) {
    this.hotCacheManager = new HotDataCacheManager(cacheType);
    this.coldDataLoader = new ColdDataLoader(cacheType);
  }

  // 处理SSE实时数据更新
  handleSSERealtimeUpdate(sseData: any[]): any[] | null {
    console.log(`[SSE实时] 收到 ${sseData.length} 条实时数据`);

    // 智能增量更新热数据缓存
    const { updatedData } = this.hotCacheManager.incrementalUpdateHotData(sseData);

    // 只有当前视口在热数据范围时才返回更新
    const viewport = this.hotCacheManager.currentViewport;
    if (viewport && viewport.isInHotRange) {
      console.log(`[SSE实时] 视口在热数据范围，更新UI`);
      return this.getCurrentViewportData();
    } else {
      console.log(`[SSE实时] 视口在冷数据范围，静默更新热数据缓存`);
      return null; // 不更新UI
    }
  }

  // 处理数据库更新通知
  async handleDatabaseUpdate(): Promise<any[] | null> {
    console.log(`[数据库更新] 收到更新通知`);

    const viewport = this.hotCacheManager.currentViewport;
    if (!viewport) {
      await this.updateHotDataCacheSilently();
      return null;
    }

    if (viewport.isFullyInColdRange) {
      // 视口完全在冷数据范围 - 静默更新，不影响UI
      console.log(`[数据库更新] 视口在冷数据范围，静默更新热数据缓存`);
      await this.updateHotDataCacheSilently();
      return null;

    } else {
      // 视口包含热数据范围 - 更新并返回数据
      console.log(`[数据库更新] 视口包含热数据范围，更新缓存并返回数据`);
      await this.updateHotDataCacheSilently();
      return this.getCurrentViewportData();
    }
  }

  // 视口变化处理
  async handleViewportChange(startTime: number, endTime: number): Promise<any[]> {
    console.log(`[视口变化] ${new Date(startTime)} - ${new Date(endTime)}`);

    // 更新视口信息
    this.hotCacheManager.updateViewport(startTime, endTime);

    // 返回视口数据
    return await this.getCurrentViewportData();
  }

  // 获取立即可用的数据（用于渐进式加载）
  getImmediatelyAvailableData(): any[] {
    const viewport = this.hotCacheManager.currentViewport;
    if (!viewport) return [];

    let availableData: any[] = [];

    // 热数据立即可用
    if (viewport.isInHotRange) {
      const hotData = this.hotCacheManager.getHotDataInViewport();
      availableData = [...availableData, ...hotData];
      console.log(`[立即可用] 热数据: ${hotData.length} 条`);
    }

    // 检查冷数据缓存
    if (viewport.isInColdRange) {
      const hotThreshold = Date.now() - HOT_DATA_THRESHOLD;
      const coldEndTime = Math.min(viewport.endTime, hotThreshold);

      const cachedColdData = this.coldDataLoader.getImmediatelyAvailableColdData(
        viewport.startTime,
        coldEndTime
      );
      availableData = [...availableData, ...cachedColdData];
      console.log(`[立即可用] 冷数据缓存: ${cachedColdData.length} 条`);
    }

    const totalAvailable = this.sortAndDeduplicate(availableData);
    console.log(`[立即可用] 总计: ${totalAvailable.length} 条数据`);

    return totalAvailable;
  }

  // 获取当前视口数据（私有方法）
  private async getCurrentViewportData(): Promise<any[]> {
    const viewport = this.hotCacheManager.currentViewport;
    if (!viewport) return [];

    const hotThreshold = Date.now() - HOT_DATA_THRESHOLD;
    let allData: any[] = [];

    // 1. 加载冷数据部分（智能加载）
    if (viewport.isInColdRange) {
      const coldEndTime = Math.min(viewport.endTime, hotThreshold);

      console.log(`[视口数据] 加载冷数据: ${new Date(viewport.startTime)} - ${new Date(coldEndTime)}`);
      const coldData = await this.coldDataLoader.loadColdData(
        viewport.startTime,
        coldEndTime
      );
      allData = [...allData, ...coldData];

      // 检查冷数据完整性
      const isComplete = this.coldDataLoader.isDataComplete(
        viewport.startTime,
        coldEndTime,
        coldData
      );
      console.log(`[视口数据] 冷数据完整性: ${isComplete}`);
    }

    // 2. 获取热数据部分
    if (viewport.isInHotRange) {
      const hotData = this.hotCacheManager.getHotDataInViewport();
      allData = [...allData, ...hotData];
      console.log(`[视口数据] 热数据: ${hotData.length} 条`);
    }

    const finalData = this.sortAndDeduplicate(allData);
    console.log(`[视口数据] 最终数据: ${finalData.length} 条`);

    return finalData;
  }

  // 静默更新热数据缓存（需要在具体实现中重写）
  private async updateHotDataCacheSilently(): Promise<void> {
    throw new Error('updateHotDataCacheSilently 方法需要在具体实现中重写');
  }

  private sortAndDeduplicate(data: any[]): any[] {
    const uniqueMap = new Map();
    data.forEach(item => {
      const key = typeof item.time === 'string' ? item.time : item.time.toString();
      uniqueMap.set(key, item);
    });

    return Array.from(uniqueMap.values()).sort((a, b) => {
      const timeA = typeof a.time === 'string' ? parseInt(a.time) : Number(a.time);
      const timeB = typeof b.time === 'string' ? parseInt(b.time) : Number(b.time);
      return timeA - timeB;
    });
  }

  // 获取缓存统计信息
  getCacheStats() {
    return this.hotCacheManager.getCacheStats();
  }

  // 强制清理所有缓存
  clearAll() {
    this.hotCacheManager.clearAll();
  }
}

// 导出缓存管理器工厂（保持向后兼容）
export const createCacheManager = (cacheType: string) => {
  return new HotDataCacheManager(cacheType);
};

// 导出新的统一数据管理器工厂
export const createUnifiedDataManager = (cacheType: string) => {
  return new UnifiedDataManager(cacheType);
};

// 导出类型和工具函数
export type { ViewportInfo, HotCacheItem, ColdCacheItem };

// 获取当前视图范围的工具函数
export const getCurrentViewRange = (chartApi?: any): [number, number] | undefined => {
  if (!chartApi) return undefined;

  try {
    const timeScale = chartApi.timeScale();
    const visibleRange = timeScale.getVisibleTimeRange();

    if (visibleRange) {
      const start = (visibleRange.from as number) * 1000;
      const end = (visibleRange.to as number) * 1000;
      return [start, end];
    }
  } catch (error) {
    console.warn('获取视图范围失败:', error);
  }

  return undefined;
};
