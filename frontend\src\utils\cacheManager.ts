// 缓存项接口
interface CacheItem {
  data: any;
  timestamp: number;  // 缓存时间戳
  accessTime: number; // 最后访问时间
}

// 热数据缓存项接口
interface HotCacheItem {
  data: any;
  timestamp: number;
  itemTime: number;
}

// 视口信息接口
interface ViewportInfo {
  startTime: number;
  endTime: number;
  isInHotRange: boolean;      // 视口是否包含热数据
  isInColdRange: boolean;     // 视口是否包含冷数据
  isFullyInHotRange: boolean; // 视口是否完全在热数据范围
  isFullyInColdRange: boolean;// 视口是否完全在冷数据范围
}

// 缓存配置 - 会话级全缓存策略
// 目标：确保用户在会话期间可以无缝滚动到任何已加载的历史位置，无数据断层
// 适用于：实时K线、预测K线、预测折线三种数据类型
const CACHE_CONFIG = {
  MAX_ITEMS: Infinity,              // 会话期间无限制缓存，保持所有已加载数据
  CACHE_TTL: 24 * 60 * 60 * 1000,  // 24小时会话级缓存（实际上只在页面关闭时清理）
  CLEANUP_THRESHOLD: Infinity,      // 永不触发自动清理，保证数据完整性
  VIEWPORT_BUFFER: Infinity,        // 保留所有数据，不基于视图范围清理
  CLEANUP_RATIO: 0                  // 不自动清理任何数据，只在页面关闭/刷新时自动清理
};

// 缓存管理类
class DataCacheManager {
  private cache = new Map<string, CacheItem>();
  private cacheType: string;
  
  constructor(cacheType: string) {
    this.cacheType = cacheType;
  }
  
  // 添加数据到缓存
  addData(data: any[]): any[] {
    const now = Date.now();
    
    // 为每个数据项添加缓存信息
    const cachedData = data.map(item => {
      const key = this.getItemKey(item);
      const existing = this.cache.get(key);
      
      // 如果已存在且未过期，更新访问时间
      if (existing && (now - existing.timestamp) < CACHE_CONFIG.CACHE_TTL) {
        existing.accessTime = now;
        existing.data = item; // 更新数据
        return existing.data;
      }
      
      // 创建新的缓存项
      const cacheItem: CacheItem = {
        data: item,
        timestamp: now,
        accessTime: now
      };
      
      this.cache.set(key, cacheItem);
      return item;
    });
    
    // 检查是否需要清理
    if (this.cache.size > CACHE_CONFIG.CLEANUP_THRESHOLD) {
      this.cleanup();
    }
    
    return cachedData;
  }
  
  // 获取所有有效数据 - 会话级全缓存版本
  // 缓存生命周期：只在页面关闭/刷新时自动清理，会话期间永久保留
  getAllValidData(): any[] {
    const now = Date.now();
    const validData: any[] = [];

    // 会话级全缓存：返回所有数据，不删除任何缓存项
    // 数据将保留直到：1) 页面刷新 2) 页面关闭 3) 浏览器关闭
    this.cache.forEach((item, key) => {
      // 更新访问时间（用于统计）
      item.accessTime = now;
      validData.push(item.data);
    });

    // 按时间排序
    return this.sortData(validData);
  }
  
  // 智能清理 - 在会话级全缓存策略下，此方法不会被自动调用
  // 只有在手动调用 clearAll() 或页面刷新时才会清理缓存
  private cleanup(currentViewRange?: [number, number]) {
    console.log(`[缓存清理] ${this.cacheType} 开始清理，当前缓存: ${this.cache.size} 条`);

    const now = Date.now();
    const itemsToRemove: string[] = [];

    // 1. 清理过期数据
    this.cache.forEach((item, key) => {
      if ((now - item.timestamp) >= CACHE_CONFIG.CACHE_TTL) {
        itemsToRemove.push(key);
      }
    });

    // 2. 如果还是太多，基于视图范围清理
    if (this.cache.size - itemsToRemove.length > CACHE_CONFIG.MAX_ITEMS && currentViewRange) {
      const [viewStart, viewEnd] = currentViewRange;
      const timeSpan = viewEnd - viewStart;
      const bufferSize = timeSpan * CACHE_CONFIG.VIEWPORT_BUFFER;

      const keepStart = viewStart - bufferSize;
      const keepEnd = viewEnd + bufferSize;

      this.cache.forEach((item, key) => {
        if (!itemsToRemove.includes(key)) {
          const itemTime = this.getItemTime(item.data);
          if (itemTime < keepStart || itemTime > keepEnd) {
            itemsToRemove.push(key);
          }
        }
      });
    }

    // 3. 如果还是太多，清理最久未访问的数据
    if (this.cache.size - itemsToRemove.length > CACHE_CONFIG.MAX_ITEMS) {
      const remainingItems: [string, CacheItem][] = [];
      this.cache.forEach((item, key) => {
        if (!itemsToRemove.includes(key)) {
          remainingItems.push([key, item]);
        }
      });

      // 按访问时间排序
      remainingItems.sort(([, a], [, b]) => a.accessTime - b.accessTime);

      const removeCount = Math.floor(remainingItems.length * CACHE_CONFIG.CLEANUP_RATIO);
      for (let i = 0; i < removeCount; i++) {
        itemsToRemove.push(remainingItems[i][0]);
      }
    }

    // 执行清理
    itemsToRemove.forEach(key => this.cache.delete(key));

    console.log(`[缓存清理] ${this.cacheType} 清理完成，删除 ${itemsToRemove.length} 条，剩余: ${this.cache.size} 条`);
  }
  
  // 获取数据项的键（兼容字符串和数字）
  private getItemKey(item: any): string {
    if (typeof item.time === 'string') {
      return item.time;
    } else if (typeof item.time === 'number') {
      return item.time.toString();
    } else {
      return String(item.time);
    }
  }
  
  // 获取数据项的时间（统一转换为数字）
  private getItemTime(item: any): number {
    if (typeof item.time === 'string') {
      return parseInt(item.time);
    } else if (typeof item.time === 'number') {
      return item.time;
    } else {
      return Number(item.time);
    }
  }
  
  // 排序数据（保持原有逻辑）
  private sortData(data: any[]): any[] {
    return data.sort((a, b) => {
      const timeA = this.getItemTime(a);
      const timeB = this.getItemTime(b);
      return timeA - timeB;
    });
  }
  
  // 获取缓存统计信息
  getCacheStats() {
    const now = Date.now();
    let validCount = 0;
    let expiredCount = 0;

    this.cache.forEach((item) => {
      if ((now - item.timestamp) < CACHE_CONFIG.CACHE_TTL) {
        validCount++;
      } else {
        expiredCount++;
      }
    });

    return {
      total: this.cache.size,
      valid: validCount,
      expired: expiredCount,
      cacheType: this.cacheType
    };
  }
  
  // 强制清理所有缓存
  clearAll() {
    this.cache.clear();
    console.log(`[缓存清理] ${this.cacheType} 强制清理所有缓存`);
  }
}

// 热数据缓存管理器
class HotDataCacheManager {
  private readonly HOT_DATA_THRESHOLD = 30 * 24 * 60 * 60 * 1000; // 30天
  private hotCache = new Map<string, HotCacheItem>();
  private currentViewport: ViewportInfo | null = null;

  constructor(private cacheType: string) {}

  // 更新视口信息
  updateViewport(startTime: number, endTime: number) {
    const hotThreshold = Date.now() - this.HOT_DATA_THRESHOLD;

    this.currentViewport = {
      startTime,
      endTime,
      isInHotRange: endTime >= hotThreshold,
      isInColdRange: startTime < hotThreshold,
      isFullyInHotRange: startTime >= hotThreshold,
      isFullyInColdRange: endTime < hotThreshold
    };

    console.log(`[视口更新] ${this.cacheType} ${new Date(startTime)} - ${new Date(endTime)}`);
    console.log(`[视口状态] ${this.cacheType} 跨越边界: ${this.currentViewport.isInHotRange && this.currentViewport.isInColdRange}`);
  }

  // 智能增量更新（核心逻辑）
  incrementalUpdateHotData(newData: any[]): { updatedData: any[], cleanedItems: any[] } {
    const now = Date.now();
    const hotThreshold = now - this.HOT_DATA_THRESHOLD;
    const cleanedItems: any[] = [];

    // 1. 增量更新热数据
    newData.forEach(item => {
      const itemTime = this.getItemTime(item);
      const key = this.getItemKey(item);

      if (itemTime >= hotThreshold) {
        const existing = this.hotCache.get(key);
        this.hotCache.set(key, {
          data: existing ? this.mergeData(existing.data, item) : item,
          timestamp: now,
          itemTime: itemTime
        });
      }
    });

    // 2. 智能清理过期数据（关键改进）
    this.hotCache.forEach((item, key) => {
      if (item.itemTime < hotThreshold) {
        if (this.shouldProtectFromCleanup(item)) {
          console.log(`[智能清理] ${this.cacheType} 保护视口内数据: ${new Date(item.itemTime)}`);
          // 不清理，保留在缓存中
        } else {
          cleanedItems.push({
            key,
            itemTime: item.itemTime,
            data: item.data
          });
          this.hotCache.delete(key);
        }
      }
    });

    if (cleanedItems.length > 0) {
      console.log(`[智能清理] ${this.cacheType} 清理了 ${cleanedItems.length} 条过期数据`);
    }

    return {
      updatedData: this.getAllHotData(),
      cleanedItems
    };
  }

  // 数据保护判断（简洁版）
  private shouldProtectFromCleanup(item: HotCacheItem): boolean {
    if (!this.currentViewport) {
      return false; // 无视口信息，不保护
    }

    // 只有当视口跨越冷热边界时才需要保护
    if (!this.currentViewport.isInColdRange || !this.currentViewport.isInHotRange) {
      return false; // 视口不跨边界，不需要保护
    }

    // 检查数据是否在视口范围内
    return (
      item.itemTime >= this.currentViewport.startTime &&
      item.itemTime <= this.currentViewport.endTime
    );
  }

  // 获取视口范围内的热数据
  getHotDataInViewport(): any[] {
    if (!this.currentViewport) return [];

    const { startTime, endTime } = this.currentViewport;
    const hotThreshold = Date.now() - this.HOT_DATA_THRESHOLD;

    return Array.from(this.hotCache.values())
      .filter(item => {
        const itemTime = item.itemTime;
        return itemTime >= Math.max(startTime, hotThreshold) && itemTime <= endTime;
      })
      .map(item => item.data)
      .sort((a, b) => this.getItemTime(a) - this.getItemTime(b));
  }

  // 获取所有热数据
  getAllHotData(): any[] {
    return Array.from(this.hotCache.values())
      .map(item => item.data)
      .sort((a, b) => this.getItemTime(a) - this.getItemTime(b));
  }

  // 获取当前视口信息
  getCurrentViewport(): ViewportInfo | null {
    return this.currentViewport;
  }

  // 获取热数据阈值
  getHotDataThreshold(): number {
    return this.HOT_DATA_THRESHOLD;
  }

  private getItemKey(item: any): string {
    return typeof item.time === 'string' ? item.time : item.time.toString();
  }

  private getItemTime(item: any): number {
    return typeof item.time === 'string' ? parseInt(item.time) : Number(item.time);
  }

  private mergeData(existing: any, newItem: any): any {
    // 实时数据优先，保留最新的价格信息
    return { ...existing, ...newItem };
  }
}

// 导出缓存管理器工厂
export const createCacheManager = (cacheType: string) => {
  return new DataCacheManager(cacheType);
};

// 冷数据缓存项接口
interface ColdCacheItem {
  data: any[];
  timestamp: number;
  lastAccess: number;
  range: [number, number];
}

// 冷数据按需加载器
class ColdDataLoader {
  private coldDataCache = new Map<string, ColdCacheItem>();
  private readonly COLD_DATA_TTL = 5 * 60 * 1000; // 5分钟TTL
  private loadingPromises = new Map<string, Promise<any[]>>(); // 防止重复加载

  constructor(private cacheType: string) {}

  // 按需加载冷数据（增强版）
  async loadColdData(startTime: number, endTime: number): Promise<any[]> {
    const cacheKey = `${startTime}-${endTime}`;

    // 1. 检查缓存
    const cached = this.coldDataCache.get(cacheKey);
    if (cached && this.isCacheValid(cached)) {
      cached.lastAccess = Date.now();
      console.log(`[冷数据] ${this.cacheType} 缓存命中: ${cached.data.length} 条数据`);
      return cached.data;
    }

    // 2. 检查是否正在加载中
    const existingPromise = this.loadingPromises.get(cacheKey);
    if (existingPromise) {
      console.log(`[冷数据] ${this.cacheType} 等待正在进行的加载: ${cacheKey}`);
      return await existingPromise;
    }

    // 3. 检查是否有部分缓存可用
    const partialData = this.getPartialCachedData(startTime, endTime);
    const missingRanges = this.calculateMissingRanges(startTime, endTime, partialData);

    if (missingRanges.length === 0) {
      console.log(`[冷数据] ${this.cacheType} 部分缓存完全覆盖请求范围`);
      return partialData;
    }

    // 4. 加载缺失的数据
    console.log(`[冷数据] ${this.cacheType} 需要加载 ${missingRanges.length} 个缺失范围`);
    const loadPromise = this.loadMissingData(missingRanges, partialData);
    this.loadingPromises.set(cacheKey, loadPromise);

    try {
      const completeData = await loadPromise;

      // 缓存完整数据
      this.coldDataCache.set(cacheKey, {
        data: completeData,
        timestamp: Date.now(),
        lastAccess: Date.now(),
        range: [startTime, endTime]
      });

      this.cleanupExpiredColdData();
      return completeData;

    } finally {
      this.loadingPromises.delete(cacheKey);
    }
  }

  // 获取立即可用的缓存数据（用于渐进式显示）
  getImmediatelyAvailableColdData(startTime: number, endTime: number): any[] {
    const partialData = this.getPartialCachedData(startTime, endTime);

    if (partialData.length > 0) {
      console.log(`[立即可用] ${this.cacheType} 找到 ${partialData.length} 条缓存的冷数据`);
    } else {
      console.log(`[立即可用] ${this.cacheType} 无可用的冷数据缓存`);
    }

    return partialData;
  }

  // 检查数据完整性
  isDataComplete(startTime: number, endTime: number, data: any[]): boolean {
    const missingRanges = this.calculateMissingRanges(startTime, endTime, data);
    return missingRanges.length === 0;
  }

  // 获取部分缓存数据
  private getPartialCachedData(startTime: number, endTime: number): any[] {
    const now = Date.now();
    let partialData: any[] = [];

    this.coldDataCache.forEach((item, key) => {
      if (now - item.lastAccess <= this.COLD_DATA_TTL) {
        const [cacheStart, cacheEnd] = item.range;

        // 检查范围重叠
        if (cacheStart < endTime && cacheEnd > startTime) {
          const filteredData = item.data.filter(dataItem => {
            const itemTime = this.getItemTime(dataItem);
            return itemTime >= startTime && itemTime <= endTime;
          });

          partialData = [...partialData, ...filteredData];
          console.log(`[部分缓存] ${this.cacheType} 从缓存 ${key} 获取 ${filteredData.length} 条数据`);
        }
      }
    });

    return this.sortAndDeduplicate(partialData);
  }

  // 计算缺失的数据范围
  private calculateMissingRanges(
    startTime: number,
    endTime: number,
    existingData: any[]
  ): Array<[number, number]> {
    if (existingData.length === 0) {
      return [[startTime, endTime]];
    }

    const KLINE_INTERVAL = 30 * 60 * 1000; // 30分钟K线间隔
    const sortedData = existingData.sort((a, b) => this.getItemTime(a) - this.getItemTime(b));
    const missingRanges: Array<[number, number]> = [];

    // 检查开头是否缺失
    const firstDataTime = this.getItemTime(sortedData[0]);
    if (firstDataTime > startTime + KLINE_INTERVAL) {
      missingRanges.push([startTime, firstDataTime - KLINE_INTERVAL]);
    }

    // 检查中间是否有断层
    for (let i = 0; i < sortedData.length - 1; i++) {
      const currentTime = this.getItemTime(sortedData[i]);
      const nextTime = this.getItemTime(sortedData[i + 1]);
      const expectedNextTime = currentTime + KLINE_INTERVAL;

      if (nextTime > expectedNextTime + KLINE_INTERVAL) {
        missingRanges.push([expectedNextTime, nextTime - KLINE_INTERVAL]);
      }
    }

    // 检查结尾是否缺失
    const lastDataTime = this.getItemTime(sortedData[sortedData.length - 1]);
    if (lastDataTime < endTime - KLINE_INTERVAL) {
      missingRanges.push([lastDataTime + KLINE_INTERVAL, endTime]);
    }

    if (missingRanges.length > 0) {
      console.log(`[缺失检测] ${this.cacheType} 发现 ${missingRanges.length} 个缺失范围:`);
      missingRanges.forEach((range, index) => {
        console.log(`[缺失${index + 1}] ${new Date(range[0])} - ${new Date(range[1])}`);
      });
    }

    return missingRanges;
  }

  // 加载缺失的数据
  private async loadMissingData(
    missingRanges: Array<[number, number]>,
    existingData: any[]
  ): Promise<any[]> {
    let allData = [...existingData];

    // 并行加载所有缺失范围
    const loadPromises = missingRanges.map(async ([rangeStart, rangeEnd]) => {
      try {
        console.log(`[缺失加载] ${this.cacheType} 加载范围: ${new Date(rangeStart)} - ${new Date(rangeEnd)}`);
        const data = await this.fetchFromServer(rangeStart, rangeEnd);
        console.log(`[缺失加载] ${this.cacheType} 加载完成: ${data.length} 条数据`);
        return data;
      } catch (error) {
        console.error(`[缺失加载] ${this.cacheType} 失败: ${new Date(rangeStart)} - ${new Date(rangeEnd)}`, error);
        return [];
      }
    });

    const missingDataArrays = await Promise.all(loadPromises);

    // 合并所有数据
    missingDataArrays.forEach(missingData => {
      allData = [...allData, ...missingData];
    });

    return this.sortAndDeduplicate(allData);
  }

  private cleanupExpiredColdData() {
    const now = Date.now();
    let cleanedCount = 0;

    this.coldDataCache.forEach((item, key) => {
      if (now - item.lastAccess > this.COLD_DATA_TTL) {
        this.coldDataCache.delete(key);
        cleanedCount++;
      }
    });

    if (cleanedCount > 0) {
      console.log(`[冷数据清理] ${this.cacheType} 清理了 ${cleanedCount} 条过期缓存`);
    }
  }

  private isCacheValid(cached: ColdCacheItem): boolean {
    return Date.now() - cached.lastAccess <= this.COLD_DATA_TTL;
  }

  // 设置服务器数据获取函数
  setFetchFromServer(fetchFn: (startTime: number, endTime: number) => Promise<any[]>) {
    this.fetchFromServer = fetchFn;
  }

  private async fetchFromServer(startTime: number, endTime: number): Promise<any[]> {
    // 这里需要根据具体的数据类型调用不同的API
    // 暂时抛出错误，需要在具体实现中重写
    throw new Error(`${this.cacheType} 需要实现具体的服务器请求逻辑`);
  }

  private getItemTime(item: any): number {
    return typeof item.time === 'string' ? parseInt(item.time) : Number(item.time);
  }

  private sortAndDeduplicate(data: any[]): any[] {
    const uniqueMap = new Map();
    data.forEach(item => {
      const key = typeof item.time === 'string' ? item.time : item.time.toString();
      uniqueMap.set(key, item);
    });

    return Array.from(uniqueMap.values()).sort((a, b) =>
      this.getItemTime(a) - this.getItemTime(b)
    );
  }
}

// 导出热数据缓存管理器工厂
export const createHotDataCacheManager = (cacheType: string) => {
  return new HotDataCacheManager(cacheType);
};

// 导出冷数据加载器工厂
export const createColdDataLoader = (cacheType: string) => {
  return new ColdDataLoader(cacheType);
};

// 获取当前视图范围的工具函数
export const getCurrentViewRange = (chartApi?: any): [number, number] | undefined => {
  if (!chartApi) return undefined;
  
  try {
    const timeScale = chartApi.timeScale();
    const visibleRange = timeScale.getVisibleTimeRange();
    
    if (visibleRange) {
      const start = (visibleRange.from as number) * 1000;
      const end = (visibleRange.to as number) * 1000;
      return [start, end];
    }
  } catch (error) {
    console.warn('获取视图范围失败:', error);
  }
  
  return undefined;
};
