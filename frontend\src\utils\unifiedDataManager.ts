import { createHotDataCacheManager, createColdDataLoader } from './cacheManager';
import { fetchThirtyMinKlineData } from '../services/api';

// 数据状态接口
interface DataState {
  data: any[];
  isLoading: boolean;
  error: string | null;
}

// 统一数据管理器
export class UnifiedDataManager {
  private hotCacheManager: any;
  private coldDataLoader: any;
  
  constructor(private dataType: string) {
    this.hotCacheManager = createHotDataCacheManager(dataType);
    this.coldDataLoader = createColdDataLoader(dataType);
    
    // 为冷数据加载器设置具体的API调用逻辑
    this.setupColdDataFetcher();
  }
  
  // 设置冷数据获取逻辑
  private setupColdDataFetcher() {
    // 使用setFetchFromServer方法设置获取函数
    this.coldDataLoader.setFetchFromServer(async (startTime: number, endTime: number): Promise<any[]> => {
      if (this.dataType === '实时K线') {
        // K线数据使用现有API
        const response = await fetchThirtyMinKlineData(endTime); // 使用endTime作为before参数
        return response || [];
      } else {
        // 其他数据类型暂时返回空数组
        console.warn(`${this.dataType} 的冷数据加载逻辑尚未实现`);
        return [];
      }
    });
  }
  
  // 处理SSE实时数据更新
  handleSSERealtimeUpdate(sseData: any[]): any[] | null {
    console.log(`[SSE实时] ${this.dataType} 收到 ${sseData.length} 条实时数据`);

    // 增量更新热数据缓存
    const { updatedData } = this.hotCacheManager.incrementalUpdateHotData(sseData);

    // 只有当前视口在热数据范围时才返回更新
    const viewport = this.hotCacheManager.getCurrentViewport();
    if (viewport && viewport.isInHotRange) {
      console.log(`[SSE实时] ${this.dataType} 视口在热数据范围，更新UI`);
      // 对于SSE实时更新，只返回热数据（同步操作）
      return this.getImmediateViewportData();
    } else {
      console.log(`[SSE实时] ${this.dataType} 视口在冷数据范围，静默更新热数据缓存`);
      return null; // 不更新UI
    }
  }
  
  // 处理数据库更新通知
  async handleDatabaseUpdate(): Promise<any[] | null> {
    console.log(`[数据库更新] ${this.dataType} 收到更新通知`);
    
    const viewport = this.hotCacheManager.getCurrentViewport();
    if (!viewport) {
      console.log(`[数据库更新] ${this.dataType} 无视口信息，静默更新热数据`);
      await this.updateHotDataCacheSilently();
      return null;
    }
    
    if (viewport.isFullyInColdRange) {
      // 视口完全在冷数据范围 - 静默更新，不影响UI
      console.log(`[数据库更新] ${this.dataType} 视口在冷数据范围，静默更新热数据缓存`);
      await this.updateHotDataCacheSilently();
      return null;
      
    } else {
      // 视口包含热数据范围 - 更新并返回数据
      console.log(`[数据库更新] ${this.dataType} 视口包含热数据范围，更新缓存并返回数据`);
      await this.updateHotDataCacheSilently();
      return this.getCurrentViewportData();
    }
  }
  
  // 视口变化处理
  async handleViewportChange(startTime: number, endTime: number): Promise<any[]> {
    console.log(`[视口变化] ${this.dataType} ${new Date(startTime)} - ${new Date(endTime)}`);
    
    // 更新视口信息
    this.hotCacheManager.updateViewport(startTime, endTime);
    
    // 返回视口数据
    return await this.getCurrentViewportData();
  }
  
  // 获取当前视口数据
  private async getCurrentViewportData(): Promise<any[]> {
    const viewport = this.hotCacheManager.getCurrentViewport();
    if (!viewport) return [];
    
    const hotThreshold = Date.now() - this.hotCacheManager.getHotDataThreshold();
    let allData: any[] = [];
    
    // 加载冷数据部分
    if (viewport.isInColdRange) {
      const coldEndTime = Math.min(viewport.endTime, hotThreshold);
      
      console.log(`[视口数据] ${this.dataType} 加载冷数据: ${new Date(viewport.startTime)} - ${new Date(coldEndTime)}`);
      const coldData = await this.coldDataLoader.loadColdData(
        viewport.startTime, 
        coldEndTime
      );
      allData = [...allData, ...coldData];
      
      // 检查冷数据完整性
      const isComplete = this.coldDataLoader.isDataComplete(
        viewport.startTime, 
        coldEndTime, 
        coldData
      );
      console.log(`[视口数据] ${this.dataType} 冷数据完整性: ${isComplete}`);
    }
    
    // 获取热数据部分
    if (viewport.isInHotRange) {
      const hotData = this.hotCacheManager.getHotDataInViewport();
      allData = [...allData, ...hotData];
      console.log(`[视口数据] ${this.dataType} 热数据: ${hotData.length} 条`);
    }
    
    const finalData = this.sortAndDeduplicate(allData);
    console.log(`[视口数据] ${this.dataType} 最终数据: ${finalData.length} 条`);
    
    return finalData;
  }
  
  // 获取立即可用的视口数据（同步操作，用于SSE更新）
  private getImmediateViewportData(): any[] {
    const viewport = this.hotCacheManager.getCurrentViewport();
    if (!viewport) return [];

    let availableData: any[] = [];

    // 1. 热数据立即可用
    if (viewport.isInHotRange) {
      const hotData = this.hotCacheManager.getHotDataInViewport();
      availableData = [...availableData, ...hotData];
      console.log(`[立即视口] ${this.dataType} 热数据: ${hotData.length} 条`);
    }

    // 2. 检查冷数据缓存（仅缓存中的数据）
    if (viewport.isInColdRange) {
      const hotThreshold = Date.now() - this.hotCacheManager.getHotDataThreshold();
      const coldEndTime = Math.min(viewport.endTime, hotThreshold);

      const cachedColdData = this.coldDataLoader.getImmediatelyAvailableColdData(
        viewport.startTime,
        coldEndTime
      );
      availableData = [...availableData, ...cachedColdData];
      console.log(`[立即视口] ${this.dataType} 冷数据缓存: ${cachedColdData.length} 条`);
    }

    const totalAvailable = this.sortAndDeduplicate(availableData);
    console.log(`[立即视口] ${this.dataType} 总计: ${totalAvailable.length} 条数据`);

    return totalAvailable;
  }

  // 获取立即可用的数据（增强版）
  getImmediatelyAvailableData(): any[] {
    const viewport = this.hotCacheManager.getCurrentViewport();
    if (!viewport) return [];
    
    let availableData: any[] = [];
    
    // 1. 热数据立即可用
    if (viewport.isInHotRange) {
      const hotData = this.hotCacheManager.getHotDataInViewport();
      availableData = [...availableData, ...hotData];
      console.log(`[立即可用] ${this.dataType} 热数据: ${hotData.length} 条`);
    }
    
    // 2. 检查冷数据缓存
    if (viewport.isInColdRange) {
      const hotThreshold = Date.now() - this.hotCacheManager.getHotDataThreshold();
      const coldEndTime = Math.min(viewport.endTime, hotThreshold);
      
      const cachedColdData = this.coldDataLoader.getImmediatelyAvailableColdData(
        viewport.startTime, 
        coldEndTime
      );
      availableData = [...availableData, ...cachedColdData];
      console.log(`[立即可用] ${this.dataType} 冷数据缓存: ${cachedColdData.length} 条`);
    }
    
    const totalAvailable = this.sortAndDeduplicate(availableData);
    console.log(`[立即可用] ${this.dataType} 总计: ${totalAvailable.length} 条数据`);
    
    return totalAvailable;
  }
  
  // 静默更新热数据缓存
  private async updateHotDataCacheSilently(): Promise<void> {
    try {
      const latestHotData = await this.loadHotDataFromServer();
      this.hotCacheManager.incrementalUpdateHotData(latestHotData);
      console.log(`[静默更新] ${this.dataType} 热数据缓存已更新`);
    } catch (error) {
      console.error(`[静默更新] ${this.dataType} 失败:`, error);
    }
  }
  
  private async loadHotDataFromServer(): Promise<any[]> {
    if (this.dataType === '实时K线') {
      const response = await fetchThirtyMinKlineData();
      return response || [];
    } else {
      // 其他数据类型暂时返回空数组
      console.warn(`${this.dataType} 的热数据加载逻辑尚未实现`);
      return [];
    }
  }
  
  private sortAndDeduplicate(data: any[]): any[] {
    const uniqueMap = new Map();
    data.forEach(item => {
      const key = typeof item.time === 'string' ? item.time : item.time.toString();
      uniqueMap.set(key, item);
    });
    
    return Array.from(uniqueMap.values()).sort((a, b) => {
      const timeA = typeof a.time === 'string' ? parseInt(a.time) : Number(a.time);
      const timeB = typeof b.time === 'string' ? parseInt(b.time) : Number(b.time);
      return timeA - timeB;
    });
  }
}
