import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import User from '../models/User';
import logger from '../utils/logger';
import sseEventManager, { SSEEventData } from './sseEventManager';

/**
 * SSE连接接口
 */
interface SSEConnection {
  id: string;
  response: Response;
  userId?: string;
  userEmail?: string;
  userRole?: string;
  lastHeartbeat: number;
  isActive: boolean;
  subscriptions: Set<string>; // 用户订阅的数据类型
}

/**
 * 统一SSE推送服务
 * 管理SSE连接并推送多种类型的实时数据（K线、预测K线、预测折线）
 */
class UnifiedSSEService {
  private connections = new Map<string, SSEConnection>();
  private heartbeatInterval: NodeJS.Timeout;
  private readonly HEARTBEAT_INTERVAL = 30 * 1000; // 30秒心跳
  private readonly CONNECTION_TIMEOUT = 60 * 1000; // 60秒连接超时

  constructor() {
    // 启动心跳检测
    this.heartbeatInterval = setInterval(() => {
      this.sendHeartbeat();
      this.cleanupDeadConnections();
    }, this.HEARTBEAT_INTERVAL);

    // 订阅SSE事件管理器的事件
    this.setupEventSubscriptions();

    logger.info('统一SSE服务已启动');
  }

  /**
   * 设置事件订阅
   */
  private setupEventSubscriptions(): void {
    // 订阅K线数据更新事件
    sseEventManager.onKlineUpdate((eventData: SSEEventData) => {
      this.broadcastKlineUpdate(eventData.data);
    });

    // 预测数据推送已移除，改为仅通过数据库更新通知

    // 订阅数据库更新事件
    sseEventManager.onDatabaseUpdate((eventData: SSEEventData) => {
      this.broadcastDatabaseUpdate(eventData.data);
    });

    logger.info('SSE事件订阅已设置完成');
  }

  /**
   * 创建SSE连接
   * @param req Express请求对象
   * @param res Express响应对象
   */
  async createConnection(req: Request, res: Response): Promise<void> {
    try {
      // 设置SSE响应头
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': process.env.FRONTEND_URL || 'http://localhost:3000',
        'Access-Control-Allow-Credentials': 'true',
        'X-Accel-Buffering': 'no' // 禁用nginx缓冲
      });

      // 生成连接ID
      const connectionId = this.generateConnectionId();
      
      // 尝试验证用户身份（可选）
      let user: any = null;

      try {
        const token = req.query.token as string || req.headers.authorization?.replace('Bearer ', '');
        if (token) {
          // 使用访问token专用密钥验证
          const decoded = jwt.verify(token, process.env.JWT_ACCESS_SECRET || 'fallback-access-secret') as any;
          user = await User.findById(decoded.userId);
        }
      } catch (error) {
        // 用户未认证或token无效，但仍允许连接（K线数据对所有用户开放）
        logger.debug('SSE连接用户身份验证失败，但允许匿名连接');
      }

      // 创建连接记录
      const connection: SSEConnection = {
        id: connectionId,
        response: res,
        userId: user?._id?.toString(),
        userEmail: user?.email,
        userRole: user?.role,
        lastHeartbeat: Date.now(),
        isActive: true,
        subscriptions: new Set()
      };

      // 根据用户权限设置数据订阅
      // 所有用户都可以订阅K线数据
      connection.subscriptions.add('kline');

      // 只有非normal用户可以订阅预测数据
      if (user && user.role !== 'normal') {
        connection.subscriptions.add('prediction');
        connection.subscriptions.add('prediction-line');
      }

      this.connections.set(connectionId, connection);

      // 发送连接成功消息
      this.sendToConnection(connectionId, {
        type: 'connection',
        data: { status: 'connected', connectionId },
        timestamp: Date.now()
      });

      // 处理连接关闭
      req.on('close', () => {
        this.removeConnection(connectionId);
      });

      req.on('error', () => {
        this.removeConnection(connectionId);
      });

    } catch (error) {
      logger.error('创建SSE连接失败', error);
      res.status(500).end();
    }
  }

  /**
   * 广播K线数据更新
   * @param klineData K线数据
   */
  broadcastKlineUpdate(klineData: any[]): void {
    const message = {
      type: 'kline-update',
      data: klineData,
      timestamp: Date.now()
    };

    let successCount = 0;
    let failureCount = 0;

    for (const [connectionId, connection] of this.connections) {
      if (connection.isActive) {
        try {
          connection.response.write(`data: ${JSON.stringify(message)}\n\n`);
          successCount++;
        } catch (error) {
          logger.error(`发送K线数据到连接 ${connectionId} 失败`, error);
          this.removeConnection(connectionId);
          failureCount++;
        }
      }
    }

    logger.debug(`K线数据广播完成`, {
      dataPoints: klineData.length,
      successCount,
      failureCount,
      totalConnections: this.connections.size
    });
  }

  /**
   * 广播数据库更新通知
   * 通知前端数据库中的K线数据已更新，前端应重新加载历史数据
   * @param updateInfo 更新信息，包含新完整K线数量等详细信息
   */
  broadcastDatabaseUpdate(updateInfo?: any): void {
    const message = {
      type: 'database-update',
      data: {
        message: '数据库K线数据已更新',
        ...updateInfo
      },
      timestamp: Date.now()
    };

    this.broadcastToSubscribers('kline', message);
  }

  // 预测数据广播方法已移除，改为仅通过数据库更新通知

  /**
   * 向特定订阅类型的用户广播消息
   * @param subscriptionType 订阅类型
   * @param message 消息内容
   */
  private broadcastToSubscribers(subscriptionType: string, message: any): void {
    let successCount = 0;
    let failureCount = 0;

    for (const [connectionId, connection] of this.connections) {
      if (connection.isActive && connection.subscriptions.has(subscriptionType)) {
        try {
          connection.response.write(`data: ${JSON.stringify(message)}\n\n`);
          successCount++;
        } catch (error) {
          logger.error(`发送${subscriptionType}数据到连接 ${connectionId} 失败`, error);
          this.removeConnection(connectionId);
          failureCount++;
        }
      }
    }

    logger.debug(`${subscriptionType}数据广播完成`, {
      successCount,
      failureCount,
      totalConnections: this.connections.size
    });
  }

  /**
   * 获取特定订阅类型的订阅者数量
   * @param subscriptionType 订阅类型
   */
  private getSubscriberCount(subscriptionType: string): number {
    let count = 0;
    for (const connection of this.connections.values()) {
      if (connection.isActive && connection.subscriptions.has(subscriptionType)) {
        count++;
      }
    }
    return count;
  }

  /**
   * 发送心跳消息
   */
  private sendHeartbeat(): void {
    const heartbeatMessage = {
      type: 'heartbeat',
      data: { timestamp: Date.now() },
      timestamp: Date.now()
    };

    for (const [connectionId, connection] of this.connections) {
      if (connection.isActive) {
        try {
          connection.response.write(`data: ${JSON.stringify(heartbeatMessage)}\n\n`);
          connection.lastHeartbeat = Date.now();
        } catch (error) {
          logger.debug(`心跳发送失败，标记连接为非活跃: ${connectionId}`);
          connection.isActive = false;
        }
      }
    }
  }

  /**
   * 清理死连接
   */
  private cleanupDeadConnections(): void {
    const now = Date.now();
    const deadConnections: string[] = [];

    for (const [connectionId, connection] of this.connections) {
      if (!connection.isActive || (now - connection.lastHeartbeat > this.CONNECTION_TIMEOUT)) {
        deadConnections.push(connectionId);
      }
    }

    deadConnections.forEach(connectionId => {
      this.removeConnection(connectionId);
    });

    if (deadConnections.length > 0) {
      logger.debug(`清理了 ${deadConnections.length} 个死连接`);
    }
  }

  /**
   * 向指定连接发送消息
   * @param connectionId 连接ID
   * @param message 消息内容
   */
  private sendToConnection(connectionId: string, message: any): void {
    const connection = this.connections.get(connectionId);
    if (connection && connection.isActive) {
      try {
        connection.response.write(`data: ${JSON.stringify(message)}\n\n`);
      } catch (error) {
        logger.error(`发送消息到连接 ${connectionId} 失败`, error);
        this.removeConnection(connectionId);
      }
    }
  }

  /**
   * 移除连接
   * @param connectionId 连接ID
   */
  private removeConnection(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (connection) {
      try {
        connection.response.end();
      } catch (error) {
        // 忽略关闭连接时的错误
      }
      
      this.connections.delete(connectionId);
    }
  }

  /**
   * 生成连接ID
   */
  private generateConnectionId(): string {
    return `sse_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 获取连接统计信息
   */
  getStats(): {
    totalConnections: number;
    activeConnections: number;
    authenticatedConnections: number;
    anonymousConnections: number;
  } {
    let activeConnections = 0;
    let authenticatedConnections = 0;
    let anonymousConnections = 0;

    for (const connection of this.connections.values()) {
      if (connection.isActive) {
        activeConnections++;
      }
      
      if (connection.userId) {
        authenticatedConnections++;
      } else {
        anonymousConnections++;
      }
    }

    return {
      totalConnections: this.connections.size,
      activeConnections,
      authenticatedConnections,
      anonymousConnections
    };
  }

  /**
   * 销毁SSE服务
   */
  destroy(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    // 关闭所有连接
    for (const connectionId of this.connections.keys()) {
      this.removeConnection(connectionId);
    }

    logger.info('K线SSE服务已销毁');
  }
}

// 导出单例实例
export default new UnifiedSSEService();
