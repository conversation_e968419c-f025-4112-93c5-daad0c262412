import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import CyberAuthForm from '../components/CyberAuthForm';
import useUserStore from '../store/useUserStore';
import api from '../api/config';

const ResetPassword: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { resetPassword, isLoading, error, clearError } = useUserStore();

  // 从URL获取token和语言参数
  const tokenFromParams = searchParams.get('token');
  const langFromParams = searchParams.get('lang');

  const [resetSuccess, setResetSuccess] = useState(false);
  const [tokenExpired, setTokenExpired] = useState(false);
  const [tokenValidating, setTokenValidating] = useState(!!tokenFromParams);
  const [countdown, setCountdown] = useState(5);
  // token状态已移除，直接使用URL参数
  const [passwordMinLength, setPasswordMinLength] = useState(6);
  const [passwordRequireSpecialChar, setPasswordRequireSpecialChar] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [errorTimer, setErrorTimer] = useState<NodeJS.Timeout | null>(null);
  
  // 处理URL中的语言参数
  useEffect(() => {
    if (langFromParams && (langFromParams === 'en' || langFromParams === 'zh')) {
      i18n.changeLanguage(langFromParams);
    }
  }, [langFromParams, i18n]);

  // 清除之前的错误
  useEffect(() => {
    clearError();
  }, [clearError]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (errorTimer) {
        clearTimeout(errorTimer);
      }
    };
  }, [errorTimer]);

  // 设置表单错误，5秒后自动清除
  const setFormErrorWithTimeout = (message: string) => {
    // 清除之前的定时器
    if (errorTimer) {
      clearTimeout(errorTimer);
    }

    // 设置错误信息
    setFormError(message);

    // 5秒后清除错误信息
    const timer = setTimeout(() => {
      setFormError(null);
      setErrorTimer(null);
    }, 5000);

    setErrorTimer(timer);
  };

  // 手动清除表单错误
  const clearFormError = () => {
    if (errorTimer) {
      clearTimeout(errorTimer);
      setErrorTimer(null);
    }
    setFormError(null);
  };

  // 处理表单输入变化，清除错误提示
  const handleFormChange = (formData: Record<string, string>) => {
    // 当用户开始输入时，清除表单错误
    if (formError) {
      clearFormError();
    }
  };

  // 加载用户设置和验证token
  useEffect(() => {
    const loadUserSettings = async () => {
      try {
        // 重置密码页面使用默认设置，因为用户可能未登录
        setPasswordMinLength(8);
        setPasswordRequireSpecialChar(false);
      } catch (error) {
        console.error('设置密码要求失败:', error);
        // 使用默认值
        setPasswordMinLength(8);
        setPasswordRequireSpecialChar(false);
      }
    };

    const validateToken = async () => {
      if (!tokenFromParams) {
        setTokenValidating(false);
        return;
      }

      try {
        // 验证token是否有效
        await api.post('/auth/validate-reset-token', { token: tokenFromParams });
        setTokenValidating(false);
      } catch (error) {
        console.error('Token验证失败:', error);
        setTokenExpired(true);
        setTokenValidating(false);

        // 启动5秒倒计时
        let timeLeft = 5;
        setCountdown(timeLeft);

        const timer = setInterval(() => {
          timeLeft -= 1;
          setCountdown(timeLeft);

          if (timeLeft <= 0) {
            clearInterval(timer);
            navigate('/login');
          }
        }, 1000);
      }
    };

    loadUserSettings();
    validateToken();
  }, [tokenFromParams, navigate]);

  // 如果正在验证token，显示加载状态
  if (tokenValidating) {
    return (
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-cyber-cyan"></div>
    );
  }

  // 如果没有token或token失效，显示统一的错误信息
  if (!tokenFromParams || tokenExpired) {

    return (
      <div className="w-full max-w-md bg-cyber-card/40 backdrop-blur-xl shadow-2xl border border-cyber-border/50 rounded-2xl p-8 text-center relative overflow-hidden">
        {/* Card glow effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-red-500/5 via-transparent to-red-600/5 rounded-2xl"></div>
        <div className="absolute -inset-1 bg-gradient-to-br from-red-500/20 via-transparent to-red-600/20 rounded-2xl blur-sm opacity-30"></div>

        <div className="relative z-10">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" viewBox="0 0 24 24" stroke="currentColor">
              <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <h2 className="text-2xl font-bold mb-4 text-cyber-text font-sans">{t('auth.resetLinkExpired')}</h2>
          <p className="text-cyber-muted mb-4 font-mono">{t('auth.resetLinkExpiredDesc')}</p>
          <p className="text-sm text-cyber-muted font-mono">{t('auth.autoRedirectIn5s').replace('5', countdown.toString())}</p>
        </div>
      </div>
    );
  }
  
  // 处理重置密码表单提交
  const handleSubmit = async (formData: Record<string, string>) => {
    try {
      // 清除之前的表单错误和定时器
      if (errorTimer) {
        clearTimeout(errorTimer);
        setErrorTimer(null);
      }
      setFormError(null);
      clearError();

      // 验证密码
      if (formData.password !== formData.confirmPassword) {
        setFormErrorWithTimeout(t('auth.passwordMismatch'));
        return;
      }

      // 验证密码长度
      if (formData.password.length < passwordMinLength) {
        setFormErrorWithTimeout(t('auth.passwordTooShort', { min: passwordMinLength }));
        return;
      }

      // 验证特殊字符
      if (passwordRequireSpecialChar && !/[!@#$%^&*(),.?":{}|<>]/.test(formData.password)) {
        setFormErrorWithTimeout(t('auth.passwordRequireSpecialChar'));
        return;
      }

      await resetPassword(tokenFromParams, formData.password);
      setResetSuccess(true);

      // 启动5秒倒计时
      let timeLeft = 5;
      setCountdown(timeLeft);

      const timer = setInterval(() => {
        timeLeft -= 1;
        setCountdown(timeLeft);

        if (timeLeft <= 0) {
          clearInterval(timer);
          navigate('/login');
        }
      }, 1000);
    } catch (error) {
      // 如果是token失效错误，显示失效卡片
      if (error instanceof Error &&
          (error.message.includes('已失效') ||
           error.message.includes('已使用') ||
           error.message.includes('不正确'))) {
        setTokenExpired(true);

        // 启动5秒倒计时
        let timeLeft = 5;
        setCountdown(timeLeft);

        const timer = setInterval(() => {
          timeLeft -= 1;
          setCountdown(timeLeft);

          if (timeLeft <= 0) {
            clearInterval(timer);
            navigate('/login');
          }
        }, 1000);
      }
      // 其他错误由store处理，会显示在表单中
      console.error('重置密码失败', error);
    }
  };
  
  // 重置密码表单字段配置

  const fields = [
    {
      name: 'password',
      label: t('auth.newPassword'),
      type: 'password',
      placeholder: t('auth.newPasswordPlaceholder')
    },
    {
      name: 'confirmPassword',
      label: t('auth.confirmPassword'),
      type: 'password',
      placeholder: t('auth.confirmPasswordPlaceholder')
    }
  ];
  
  // 如果密码已重置，显示成功信息
  if (resetSuccess) {
    return (
      <div className="w-full max-w-md bg-cyber-card/40 backdrop-blur-xl shadow-2xl border border-cyber-border/50 rounded-2xl p-8 text-center relative overflow-hidden">
        {/* Card glow effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-cyber-cyan/5 via-transparent to-cyber-purple/5 rounded-2xl"></div>
        <div className="absolute -inset-1 bg-gradient-to-br from-cyber-cyan/20 via-transparent to-cyber-purple/20 rounded-2xl blur-sm opacity-30"></div>

        <div className="relative z-10">
          <div className="text-cyber-green mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" viewBox="0 0 24 24" stroke="currentColor">
              <path d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h2 className="text-2xl font-bold mb-4 text-cyber-text font-sans">{t('auth.passwordResetSuccess')}</h2>
          <p className="text-cyber-muted mb-4 font-mono">{t('auth.passwordResetSuccessDesc')}</p>
          <p className="text-sm text-cyber-muted font-mono">{t('auth.autoRedirectIn5s').replace('5', countdown.toString())}</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="w-full max-w-md">
      <CyberAuthForm
        title={t('auth.resetPassword')}
        fields={fields}
        submitText={t('auth.resetPassword')}
        onSubmit={handleSubmit}
        isLoading={isLoading}
        error={formError || error}
        showLogo={false}
        onChange={handleFormChange}
      />
    </div>
  );
};

export default ResetPassword; 