import React, { useState } from 'react';
import { getUserConfig } from '../api/userConfig';
import tokenService from '../api/tokenService';
import useUserStore from '../store/useUserStore';

const TestAuth: React.FC = () => {
  const [result, setResult] = useState<string>('');
  const { user, isAuthenticated, token } = useUserStore();

  const testUserConfig = async () => {
    try {
      setResult('正在测试...');
      
      // 检查认证状态
      console.log('认证状态:', {
        isAuthenticated,
        hasUser: !!user,
        storeToken: token,
        memoryToken: tokenService.getToken(),
        tokenValid: token ? tokenService.isTokenValid(token) : false
      });
      
      const config = await getUserConfig();
      setResult(`成功获取配置: ${JSON.stringify(config, null, 2)}`);
    } catch (error) {
      console.error('测试失败:', error);
      setResult(`失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  const checkTokenStatus = () => {
    const memoryToken = tokenService.getToken();
    const storeToken = token;
    
    const status = {
      isAuthenticated,
      hasUser: !!user,
      storeToken: storeToken ? `${storeToken.substring(0, 20)}...` : null,
      memoryToken: memoryToken ? `${memoryToken.substring(0, 20)}...` : null,
      tokenValid: storeToken ? tokenService.isTokenValid(storeToken) : false,
      memoryTokenValid: memoryToken ? tokenService.isTokenValid(memoryToken) : false
    };
    
    setResult(`Token状态: ${JSON.stringify(status, null, 2)}`);
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h2>认证测试页面</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <p>认证状态: {isAuthenticated ? '已认证' : '未认证'}</p>
        <p>用户: {user ? user.email : '无'}</p>
      </div>
      
      <div style={{ marginBottom: '20px' }}>
        <button onClick={checkTokenStatus} style={{ marginRight: '10px' }}>
          检查Token状态
        </button>
        <button onClick={testUserConfig}>
          测试用户配置API
        </button>
      </div>
      
      <div style={{ 
        backgroundColor: '#f5f5f5', 
        padding: '10px', 
        borderRadius: '4px',
        whiteSpace: 'pre-wrap',
        maxHeight: '400px',
        overflow: 'auto'
      }}>
        {result || '点击按钮开始测试...'}
      </div>
    </div>
  );
};

export default TestAuth;
